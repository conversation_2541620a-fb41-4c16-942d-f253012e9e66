#!/bin/bash

# 增强版代理测试脚本 - 更详细的诊断信息
echo "🔍 增强版代理配置测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 测试计数器
total_tests=0
passed_tests=0

test_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    total_tests=$((total_tests + 1))
    
    if [ "$result" = "pass" ]; then
        echo -e "   ${GREEN}✅ $test_name${NC}"
        passed_tests=$((passed_tests + 1))
    elif [ "$result" = "fail" ]; then
        echo -e "   ${RED}❌ $test_name${NC}"
    else
        echo -e "   ${YELLOW}⚠️  $test_name${NC}"
    fi
    
    if [ -n "$details" ]; then
        echo -e "      ${CYAN}$details${NC}"
    fi
}

# 1. 检查v2ray服务状态
echo -e "${BLUE}1. 检查v2ray服务状态${NC}"
if launchctl list | grep -q com.local.v2ray; then
    service_status=$(launchctl list | grep com.local.v2ray)
    test_result "v2ray launchd服务正在运行" "pass" "$service_status"
else
    test_result "v2ray launchd服务未运行" "fail" "请运行 launchctl list | grep v2ray 检查"
fi

# 检查v2ray进程
v2ray_processes=$(ps aux | grep "v2ray run" | grep -v grep | wc -l)
if [ "$v2ray_processes" -gt 0 ]; then
    test_result "v2ray进程运行中" "pass" "发现 $v2ray_processes 个v2ray进程"
else
    test_result "v2ray进程未运行" "fail" "没有发现v2ray进程"
fi

# 2. 检查端口监听状态
echo -e "${BLUE}2. 检查端口监听状态${NC}"
socks_listener=$(lsof -i :1080 | grep LISTEN | head -1)
if [ -n "$socks_listener" ]; then
    test_result "SOCKS端口1080正在监听" "pass" "$socks_listener"
else
    test_result "SOCKS端口1080未监听" "fail" "运行 lsof -i :1080 检查"
fi

http_listener=$(lsof -i :1086 | grep LISTEN | head -1)
if [ -n "$http_listener" ]; then
    test_result "HTTP端口1086正在监听" "pass" "$http_listener"
else
    test_result "HTTP端口1086未监听" "fail" "运行 lsof -i :1086 检查"
fi

# 3. 测试代理连接
echo -e "${BLUE}3. 测试代理连接${NC}"

# 先测试基础网络连接
echo -n "   测试基础网络连接: "
if timeout 5 curl -s http://www.baidu.com > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 基础网络正常${NC}"
else
    echo -e "${RED}❌ 基础网络异常${NC}"
fi

# 测试SOCKS代理 - 使用多个测试网站
echo -n "   测试SOCKS代理: "
socks_test1=$(timeout 8 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
socks_test2=$(timeout 8 curl -x socks5://127.0.0.1:1080 -s http://www.baidu.com 2>/dev/null | head -1)

if [ -n "$socks_test1" ]; then
    test_result "SOCKS代理工作正常" "pass" "外网IP: $socks_test1"
elif [ -n "$socks_test2" ]; then
    test_result "SOCKS代理部分工作" "warn" "可以访问百度但httpbin失败"
else
    test_result "SOCKS代理连接失败" "fail" "所有测试网站都无法访问"
fi

# 测试HTTP代理
echo -n "   测试HTTP代理: "
http_test1=$(timeout 8 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
http_test2=$(timeout 8 curl -x http://127.0.0.1:1086 -s http://www.baidu.com 2>/dev/null | head -1)

if [ -n "$http_test1" ]; then
    test_result "HTTP代理工作正常" "pass" "外网IP: $http_test1"
elif [ -n "$http_test2" ]; then
    test_result "HTTP代理部分工作" "warn" "可以访问百度但httpbin失败"
else
    test_result "HTTP代理连接失败" "fail" "所有测试网站都无法访问"
fi

# 4. 检查系统网络代理设置
echo -e "${BLUE}4. 检查系统网络代理设置${NC}"
web_proxy_info=$(networksetup -getwebproxy Wi-Fi)
if echo "$web_proxy_info" | grep -q "Enabled: Yes"; then
    proxy_server=$(echo "$web_proxy_info" | grep "Server:" | cut -d' ' -f2)
    proxy_port=$(echo "$web_proxy_info" | grep "Port:" | cut -d' ' -f2)
    test_result "系统HTTP代理已启用" "pass" "服务器: $proxy_server:$proxy_port"
else
    test_result "系统HTTP代理未启用" "fail" "运行 networksetup -getwebproxy Wi-Fi 检查"
fi

socks_proxy_info=$(networksetup -getsocksfirewallproxy Wi-Fi)
if echo "$socks_proxy_info" | grep -q "Enabled: Yes"; then
    socks_server=$(echo "$socks_proxy_info" | grep "Server:" | cut -d' ' -f2)
    socks_port=$(echo "$socks_proxy_info" | grep "Port:" | cut -d' ' -f2)
    test_result "系统SOCKS代理已启用" "pass" "服务器: $socks_server:$socks_port"
else
    test_result "系统SOCKS代理未启用" "fail" "运行 networksetup -getsocksfirewallproxy Wi-Fi 检查"
fi

# 5. 检查代理函数 - 详细诊断
echo -e "${BLUE}5. 检查代理函数${NC}"

# 检查函数定义的不同方式
echo "   检查函数定义方式:"

# 方式1: type命令
if type proxy_on >/dev/null 2>&1; then
    test_result "proxy_on函数已定义 (type)" "pass"
else
    test_result "proxy_on函数未定义 (type)" "fail"
fi

# 方式2: declare命令
if declare -f proxy_on >/dev/null 2>&1; then
    test_result "proxy_on函数已定义 (declare)" "pass"
else
    test_result "proxy_on函数未定义 (declare)" "fail"
fi

# 方式3: which命令
if which proxy_on >/dev/null 2>&1; then
    test_result "proxy_on命令可用 (which)" "pass"
else
    test_result "proxy_on命令不可用 (which)" "fail"
fi

# 检查helper文件
if [ -f "$HOME/.proxy_helpers.zsh" ]; then
    test_result "代理helper文件存在" "pass" "$HOME/.proxy_helpers.zsh"
    
    # 检查文件内容
    if grep -q "proxy_on()" "$HOME/.proxy_helpers.zsh"; then
        test_result "helper文件包含proxy_on函数" "pass"
    else
        test_result "helper文件不包含proxy_on函数" "fail"
    fi
else
    test_result "代理helper文件不存在" "fail" "$HOME/.proxy_helpers.zsh"
fi

# 检查环境变量
echo "   检查环境变量:"
if [ -n "${http_proxy-}" ]; then
    test_result "http_proxy已设置" "pass" "$http_proxy"
else
    test_result "http_proxy未设置" "fail"
fi

if [ -n "${all_proxy-}" ]; then
    test_result "all_proxy已设置" "pass" "$all_proxy"
else
    test_result "all_proxy未设置" "fail"
fi

# 6. 测试被墙网站访问
echo -e "${BLUE}6. 测试被墙网站访问${NC}"
twitter_test=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
if [ "$twitter_test" = "200" ] || [ "$twitter_test" = "301" ] || [ "$twitter_test" = "302" ]; then
    test_result "可以访问Twitter" "pass" "HTTP状态码: $twitter_test"
else
    test_result "Twitter访问异常" "warn" "HTTP状态码: $twitter_test"
fi

# 7. 检查开发工具配置
echo -e "${BLUE}7. 检查开发工具配置${NC}"
git_http=$(git config --global --get http.proxy 2>/dev/null || echo "未设置")
git_https=$(git config --global --get https.proxy 2>/dev/null || echo "未设置")

if [ "$git_http" != "未设置" ]; then
    test_result "Git HTTP代理已配置" "pass" "$git_http"
else
    test_result "Git HTTP代理未配置" "fail"
fi

npm_proxy=$(npm config get proxy 2>/dev/null || echo "未设置")
if [ "$npm_proxy" != "未设置" ] && [ "$npm_proxy" != "null" ]; then
    test_result "npm代理已配置" "pass" "$npm_proxy"
else
    test_result "npm代理未配置" "fail"
fi

# 8. 诊断建议
echo -e "${BLUE}8. 诊断建议${NC}"
echo "   当前shell: $SHELL"
echo "   用户: $USER"
echo "   工作目录: $PWD"

# 检查shell配置文件
for config_file in ~/.zshrc ~/.zshenv ~/.bash_profile; do
    if [ -f "$config_file" ]; then
        if grep -q "proxy" "$config_file"; then
            echo "   $config_file: 包含代理配置"
        else
            echo "   $config_file: 不包含代理配置"
        fi
    fi
done

# 总结
echo
echo -e "${BLUE}📊 测试总结${NC}"
echo -e "   总测试数: $total_tests"
echo -e "   通过测试: $passed_tests"
echo -e "   失败测试: $((total_tests - passed_tests))"

success_rate=$((passed_tests * 100 / total_tests))
if [ "$success_rate" -ge 80 ]; then
    echo -e "   ${GREEN}✅ 代理配置良好 ($success_rate%)${NC}"
elif [ "$success_rate" -ge 60 ]; then
    echo -e "   ${YELLOW}⚠️  代理配置需要改进 ($success_rate%)${NC}"
else
    echo -e "   ${RED}❌ 代理配置存在严重问题 ($success_rate%)${NC}"
fi

echo
echo -e "${BLUE}🔧 故障排除建议${NC}"
echo "1. 如果函数未定义，运行: source ~/.proxy_helpers.zsh"
echo "2. 如果连接失败，检查: lsof -i :1080 -i :1086"
echo "3. 如果服务未运行，检查: launchctl list | grep v2ray"
echo "4. 查看日志: tail -f /tmp/v2ray.log /tmp/v2ray.err"
