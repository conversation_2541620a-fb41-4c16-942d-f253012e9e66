#!/bin/bash

# Shell配置修复脚本
echo "🔧 修复Shell配置问题..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# 1. 检查当前Shell
echo -e "${BLUE}1. 检查Shell环境${NC}"
echo "当前Shell: $SHELL"
echo "当前用户: $USER"

# 2. 备份现有配置
echo -e "${BLUE}2. 备份现有配置${NC}"
if [ -f ~/.zshrc ]; then
    cp ~/.zshrc ~/.zshrc.backup_$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}✅ .zshrc已备份${NC}"
fi

if [ -f ~/.bash_profile ]; then
    cp ~/.bash_profile ~/.bash_profile.backup_$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}✅ .bash_profile已备份${NC}"
fi

# 3. 创建干净的.zshrc配置
echo -e "${BLUE}3. 创建优化的.zshrc配置${NC}"
cat > ~/.zshrc << 'EOF'
# zsh配置文件 - 优化版本

# 基础环境设置
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# PATH设置
export PATH="/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:$HOME/bin"

# 如果存在conda，初始化conda
if [ -f "$HOME/anaconda3/etc/profile.d/conda.sh" ]; then
    . "$HOME/anaconda3/etc/profile.d/conda.sh"
elif [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
    . "$HOME/miniconda3/etc/profile.d/conda.sh"
fi

# Python环境信息（可选显示）
if command -v python3 &> /dev/null; then
    echo "=== 当前Python环境 ==="
    echo "Python路径: $(which python3)"
    if python3 -c "import flask" 2>/dev/null; then
        echo "Flask状态: 已安装"
    else
        echo "Flask状态: 未安装"
    fi
    echo "===================="
fi

# 代理设置 - 优化为全SOCKS代理
export http_proxy=socks5://127.0.0.1:1080
export https_proxy=socks5://127.0.0.1:1080
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16

# 代理管理函数
proxy_on() {
    export http_proxy=socks5://127.0.0.1:1080
    export https_proxy=socks5://127.0.0.1:1080
    export all_proxy=socks5://127.0.0.1:1080
    export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
    echo "✅ 代理已开启 (全SOCKS模式)"
}

proxy_off() {
    unset http_proxy https_proxy all_proxy no_proxy
    echo "✅ 代理已关闭"
}

proxy_status() {
    if [ -n "$http_proxy" ]; then
        echo "✅ 代理状态：开启 (全SOCKS模式)"
        echo "   代理地址：$http_proxy"
    else
        echo "❌ 代理状态：关闭"
    fi
}

proxy_test() {
    echo "🔍 测试SOCKS代理连接..."
    if lsof -i :1080 | grep -q LISTEN; then
        echo "✅ SOCKS服务器正在运行"
        result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
        if [ -n "$result" ]; then
            echo "✅ 代理工作正常，外网IP: $result"
        else
            echo "❌ 代理连接失败"
        fi
    else
        echo "❌ SOCKS服务器未运行"
    fi
}

# 便捷别名
alias ll='ls -la'
alias la='ls -la'
alias l='ls -l'
alias ..='cd ..'
alias ...='cd ../..'

# 代理相关别名
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'

# 确保~/bin在PATH中
if [ -d "$HOME/bin" ]; then
    export PATH="$HOME/bin:$PATH"
fi

# zsh特定设置（避免autoload错误）
if [ -n "$ZSH_VERSION" ]; then
    # 基础的zsh设置，不依赖oh-my-zsh
    setopt AUTO_CD
    setopt CORRECT
    setopt HIST_VERIFY
    
    # 简单的提示符
    PROMPT='%n@%m:%~$ '
    
    # 基础补全
    autoload -U compinit
    compinit -u 2>/dev/null || true
fi
EOF

echo -e "${GREEN}✅ .zshrc配置已创建${NC}"

# 4. 确保~/bin目录存在并设置权限
echo -e "${BLUE}4. 设置~/bin目录${NC}"
mkdir -p ~/bin
chmod 755 ~/bin

# 确保proxy脚本存在且可执行
if [ ! -f ~/bin/proxy ]; then
    echo -e "${YELLOW}⚠️  proxy脚本不存在，重新创建...${NC}"
    cat > ~/bin/proxy << 'EOF'
#!/bin/bash

case "$1" in
    on)
        export http_proxy=socks5://127.0.0.1:1080
        export https_proxy=socks5://127.0.0.1:1080
        export all_proxy=socks5://127.0.0.1:1080
        export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
        echo "✅ 代理已开启 (全SOCKS模式)"
        ;;
    off)
        unset http_proxy https_proxy all_proxy no_proxy
        echo "✅ 代理已关闭"
        ;;
    status)
        if [ -n "$http_proxy" ]; then
            echo "✅ 代理状态：开启 (全SOCKS模式)"
            echo "   代理地址：$http_proxy"
        else
            echo "❌ 代理状态：关闭"
        fi
        ;;
    test)
        echo "🔍 测试SOCKS代理连接..."
        if lsof -i :1080 | grep -q LISTEN; then
            echo "✅ SOCKS服务器正在运行"
            result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
            if [ -n "$result" ]; then
                echo "✅ 代理工作正常，外网IP: $result"
                
                # 测试被墙网站
                twitter_code=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
                if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
                    echo "✅ 可以访问Twitter (HTTP $twitter_code)"
                else
                    echo "⚠️  Twitter访问异常 (HTTP $twitter_code)"
                fi
            else
                echo "❌ 代理连接失败"
            fi
        else
            echo "❌ SOCKS服务器未运行"
        fi
        ;;
    *)
        echo "用法: proxy {on|off|status|test}"
        echo ""
        echo "命令说明："
        echo "  on     - 开启代理 (全SOCKS模式)"
        echo "  off    - 关闭代理"
        echo "  status - 查看代理状态"
        echo "  test   - 测试代理连接"
        echo ""
        echo "快捷别名："
        echo "  pon    - 等同于 proxy on"
        echo "  poff   - 等同于 proxy off"
        echo "  pstatus- 等同于 proxy status"
        echo "  ptest  - 等同于 proxy test"
        ;;
esac
EOF
fi

chmod +x ~/bin/proxy
echo -e "${GREEN}✅ proxy脚本已设置${NC}"

# 5. 测试配置
echo -e "${BLUE}5. 测试新配置${NC}"
source ~/.zshrc

echo -e "${GREEN}🎉 Shell配置修复完成！${NC}"
echo
echo -e "${BLUE}📋 修复内容：${NC}"
echo "✅ 创建了干净的.zshrc配置"
echo "✅ 修复了autoload和compinit错误"
echo "✅ 设置了正确的PATH"
echo "✅ 添加了代理管理函数"
echo "✅ 创建了便捷别名"
echo "✅ 确保了~/bin/proxy脚本可用"
echo
echo -e "${BLUE}🚀 现在可以使用：${NC}"
echo "proxy test     # 测试代理"
echo "proxy status   # 查看状态"
echo "pon           # 开启代理 (别名)"
echo "poff          # 关闭代理 (别名)"
echo "ptest         # 测试代理 (别名)"
echo
echo -e "${YELLOW}💡 建议：重新打开终端或执行 'source ~/.zshrc'${NC}"
