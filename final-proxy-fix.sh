#!/bin/bash

# 最终代理修复 - 解决所有问题
echo "🎉 最终代理修复..."

# 重新定义所有代理函数
proxy_on() {
  export http_proxy="http://127.0.0.1:1086"
  export https_proxy="http://127.0.0.1:1086"
  export all_proxy="socks5://127.0.0.1:1080"
  export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
  
  # 配置开发工具
  git config --global http.proxy "http://127.0.0.1:1086" 2>/dev/null
  git config --global https.proxy "http://127.0.0.1:1086" 2>/dev/null
  npm config set proxy "http://127.0.0.1:1086" >/dev/null 2>&1
  npm config set https-proxy "http://127.0.0.1:1086" >/dev/null 2>&1
  
  echo "✅ 代理已开启 (HTTP:1086, SOCKS:1080)"
}

proxy_off() {
  unset http_proxy https_proxy all_proxy no_proxy
  git config --global --unset http.proxy 2>/dev/null || true
  git config --global --unset https.proxy 2>/dev/null || true
  npm config delete proxy >/dev/null 2>&1 || true
  npm config delete https-proxy >/dev/null 2>&1 || true
  echo "✅ 代理已关闭"
}

proxy_status() {
  echo "=== 代理状态 ==="
  echo "http_proxy=${http_proxy:-未设置}"
  echo "https_proxy=${https_proxy:-未设置}"
  echo "all_proxy=${all_proxy:-未设置}"
  echo "no_proxy=${no_proxy:-未设置}"
  
  if [ -n "$http_proxy" ]; then
    echo "✅ 代理已开启"
  else
    echo "❌ 代理已关闭"
  fi
}

proxy_test() {
  echo "🔍 测试代理连接..."
  
  # 检查服务状态
  if ! (lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN); then
    echo "❌ 代理服务器未运行"
    return 1
  fi
  
  # 临时清除no_proxy以确保测试走代理
  local old_no_proxy="$no_proxy"
  unset no_proxy
  
  # 测试SOCKS代理
  echo -n "测试SOCKS代理: "
  socks_result=$(timeout 8 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$socks_result" ]; then
    echo "✅ 工作正常，IP: $socks_result"
  else
    echo "❌ 连接失败"
  fi
  
  # 测试HTTP代理
  echo -n "测试HTTP代理: "
  http_result=$(timeout 8 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$http_result" ]; then
    echo "✅ 工作正常，IP: $http_result"
  else
    echo "❌ 连接失败"
  fi
  
  # 测试被墙网站
  echo -n "测试Twitter访问: "
  twitter_code=$(timeout 8 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
  if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
    echo "✅ 可以访问 (HTTP $twitter_code)"
  else
    echo "⚠️  访问异常 (HTTP $twitter_code)"
  fi
  
  # 恢复no_proxy
  if [ -n "$old_no_proxy" ]; then
    export no_proxy="$old_no_proxy"
  fi
}

# 便捷别名
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'

# 自动开启代理
proxy_on

echo "✅ 所有代理函数已定义并自动开启"
echo
echo "💡 可用命令："
echo "   proxy_on / pon       # 开启代理"
echo "   proxy_off / poff     # 关闭代理"
echo "   proxy_status / pstatus # 查看状态"
echo "   proxy_test / ptest   # 测试连接"
echo
echo "🎯 当前状态："
proxy_status
