#!/bin/bash

# v2ray高级诊断和修复脚本
echo "🔍 v2ray高级诊断开始..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 1. 检查v2ray进程状态
echo -e "${BLUE}1. 检查v2ray进程状态${NC}"
active_v2ray=$(ps aux | grep "v2ray run -config config.json" | grep -v grep | head -1)
if [ -n "$active_v2ray" ]; then
    pid=$(echo $active_v2ray | awk '{print $2}')
    echo -e "   ${GREEN}✅ 主v2ray进程运行中 (PID: $pid)${NC}"
else
    echo -e "   ${RED}❌ 主v2ray进程未找到${NC}"
fi

# 检查多余进程
extra_processes=$(ps aux | grep v2ray | grep -v grep | wc -l)
echo -e "   ${YELLOW}⚠️  总共有 $extra_processes 个v2ray相关进程${NC}"

# 2. 检查端口监听状态
echo -e "${BLUE}2. 检查端口监听状态${NC}"
socks_port=$(lsof -i :1080 | grep LISTEN | head -1)
http_port=$(lsof -i :1086 | grep LISTEN | head -1)

if [ -n "$socks_port" ]; then
    echo -e "   ${GREEN}✅ SOCKS端口1080正在监听${NC}"
else
    echo -e "   ${RED}❌ SOCKS端口1080未监听${NC}"
fi

if [ -n "$http_port" ]; then
    echo -e "   ${GREEN}✅ HTTP端口1086正在监听${NC}"
else
    echo -e "   ${RED}❌ HTTP端口1086未监听${NC}"
fi

# 3. 测试代理连接（使用更可靠的方法）
echo -e "${BLUE}3. 测试代理连接${NC}"

# 测试SOCKS代理 - 使用国外IP查询服务
echo "   测试SOCKS代理..."
socks_result=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s https://api.ipify.org 2>/dev/null)
if [ -n "$socks_result" ] && [[ "$socks_result" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "   ${GREEN}✅ SOCKS代理工作正常，外网IP: $socks_result${NC}"
    socks_working=true
else
    echo -e "   ${RED}❌ SOCKS代理连接失败${NC}"
    socks_working=false
fi

# 测试HTTP代理
echo "   测试HTTP代理..."
http_result=$(timeout 10 curl -x http://127.0.0.1:1086 -s https://api.ipify.org 2>/dev/null)
if [ -n "$http_result" ] && [[ "$http_result" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "   ${GREEN}✅ HTTP代理工作正常，外网IP: $http_result${NC}"
    http_working=true
else
    echo -e "   ${RED}❌ HTTP代理连接失败${NC}"
    http_working=false
fi

# 4. 测试直连（检查网络环境）
echo -e "${BLUE}4. 测试直连网络${NC}"
direct_result=$(timeout 10 curl -s https://api.ipify.org 2>/dev/null)
if [ -n "$direct_result" ] && [[ "$direct_result" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "   ${GREEN}✅ 直连网络正常，本地IP: $direct_result${NC}"
    direct_working=true
else
    echo -e "   ${RED}❌ 直连网络失败${NC}"
    direct_working=false
fi

# 5. 测试被墙网站（真正的代理测试）
echo -e "${BLUE}5. 测试被墙网站访问${NC}"

# 测试Twitter
if [ "$socks_working" = true ]; then
    twitter_test=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
    if [ "$twitter_test" = "200" ] || [ "$twitter_test" = "301" ] || [ "$twitter_test" = "302" ]; then
        echo -e "   ${GREEN}✅ 可以通过SOCKS代理访问Twitter (HTTP $twitter_test)${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Twitter访问异常 (HTTP $twitter_test)${NC}"
    fi
fi

# 测试YouTube
if [ "$http_working" = true ]; then
    youtube_test=$(timeout 10 curl -x http://127.0.0.1:1086 -s -o /dev/null -w "%{http_code}" https://www.youtube.com 2>/dev/null)
    if [ "$youtube_test" = "200" ] || [ "$youtube_test" = "301" ] || [ "$youtube_test" = "302" ]; then
        echo -e "   ${GREEN}✅ 可以通过HTTP代理访问YouTube (HTTP $youtube_test)${NC}"
    else
        echo -e "   ${YELLOW}⚠️  YouTube访问异常 (HTTP $youtube_test)${NC}"
    fi
fi

# 6. 检查v2ray配置文件
echo -e "${BLUE}6. 检查v2ray配置${NC}"
if [ -f "config.json" ]; then
    echo -e "   ${GREEN}✅ 找到config.json配置文件${NC}"
    
    # 检查路由规则
    if grep -q "geosite:cn" config.json; then
        echo -e "   ${YELLOW}⚠️  检测到中国网站直连规则${NC}"
        echo -e "      这解释了为什么某些测试网站可能直连"
    fi
    
    # 检查outbound配置
    if grep -q "vmess" config.json; then
        echo -e "   ${GREEN}✅ 使用VMess协议${NC}"
        
        # 提取服务器地址
        server_addr=$(grep -o '"address": "[^"]*"' config.json | head -1 | cut -d'"' -f4)
        server_port=$(grep -o '"port": [0-9]*' config.json | head -1 | cut -d' ' -f2)
        if [ -n "$server_addr" ] && [ -n "$server_port" ]; then
            echo -e "   ${BLUE}📡 服务器: $server_addr:$server_port${NC}"
            
            # 测试服务器连通性
            if nc -z "$server_addr" "$server_port" 2>/dev/null; then
                echo -e "   ${GREEN}✅ 代理服务器可达${NC}"
            else
                echo -e "   ${RED}❌ 代理服务器不可达${NC}"
                echo -e "      这可能是连接失败的原因"
            fi
        fi
    fi
else
    echo -e "   ${RED}❌ 未找到config.json配置文件${NC}"
fi

# 7. 生成诊断报告和修复建议
echo -e "${BLUE}7. 诊断报告和修复建议${NC}"

if [ "$socks_working" = true ] || [ "$http_working" = true ]; then
    echo -e "   ${GREEN}🎉 代理基本工作正常！${NC}"
    
    if [ "$socks_working" = true ] && [ "$http_working" = true ]; then
        echo -e "   ${GREEN}✅ 双端口代理都正常工作${NC}"
    elif [ "$socks_working" = true ]; then
        echo -e "   ${YELLOW}⚠️  仅SOCKS代理工作，HTTP代理有问题${NC}"
    else
        echo -e "   ${YELLOW}⚠️  仅HTTP代理工作，SOCKS代理有问题${NC}"
    fi
    
    echo -e "   ${BLUE}💡 建议：${NC}"
    echo -e "      - 您的代理配置正确"
    echo -e "      - 可以正常使用浏览器和开发工具"
    echo -e "      - 之前的测试失败可能是因为路由规则或网络环境"
    
else
    echo -e "   ${RED}❌ 代理连接失败${NC}"
    echo -e "   ${BLUE}💡 修复建议：${NC}"
    
    if [ "$direct_working" = false ]; then
        echo -e "      1. 检查网络连接"
        echo -e "      2. 检查DNS设置"
    fi
    
    echo -e "      3. 检查代理服务器配置"
    echo -e "      4. 检查防火墙设置"
    echo -e "      5. 尝试重启v2ray服务"
fi

echo -e "${GREEN}🎉 诊断完成${NC}"
