#!/bin/bash

# 实用的代理验证脚本
echo "🔍 实用代理验证..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}=== 代理配置验证报告 ===${NC}"

# 1. 检查代理函数
echo -e "${BLUE}1. 代理函数检查${NC}"
functions_ok=true

if type proxy_on >/dev/null 2>&1; then
    echo -e "   ${GREEN}✅ proxy_on函数可用${NC}"
else
    echo -e "   ${RED}❌ proxy_on函数不可用${NC}"
    functions_ok=false
fi

if type proxy_off >/dev/null 2>&1; then
    echo -e "   ${GREEN}✅ proxy_off函数可用${NC}"
else
    echo -e "   ${RED}❌ proxy_off函数不可用${NC}"
    functions_ok=false
fi

if type proxy_status >/dev/null 2>&1; then
    echo -e "   ${GREEN}✅ proxy_status函数可用${NC}"
else
    echo -e "   ${RED}❌ proxy_status函数不可用${NC}"
    functions_ok=false
fi

# 2. 检查代理服务
echo -e "${BLUE}2. 代理服务检查${NC}"
if lsof -i :1080 | grep -q LISTEN; then
    echo -e "   ${GREEN}✅ SOCKS端口1080正在监听${NC}"
    socks_running=true
else
    echo -e "   ${RED}❌ SOCKS端口1080未监听${NC}"
    socks_running=false
fi

if lsof -i :1086 | grep -q LISTEN; then
    echo -e "   ${GREEN}✅ HTTP端口1086正在监听${NC}"
    http_running=true
else
    echo -e "   ${RED}❌ HTTP端口1086未监听${NC}"
    http_running=false
fi

# 3. 检查环境变量
echo -e "${BLUE}3. 环境变量检查${NC}"
if [ -n "$http_proxy" ]; then
    echo -e "   ${GREEN}✅ http_proxy已设置: $http_proxy${NC}"
else
    echo -e "   ${RED}❌ http_proxy未设置${NC}"
fi

if [ -n "$all_proxy" ]; then
    echo -e "   ${GREEN}✅ all_proxy已设置: $all_proxy${NC}"
else
    echo -e "   ${RED}❌ all_proxy未设置${NC}"
fi

# 4. 检查系统代理
echo -e "${BLUE}4. 系统代理检查${NC}"
if networksetup -getwebproxy Wi-Fi | grep -q "Enabled: Yes"; then
    echo -e "   ${GREEN}✅ 系统HTTP代理已启用${NC}"
else
    echo -e "   ${YELLOW}⚠️  系统HTTP代理未启用${NC}"
fi

if networksetup -getsocksfirewallproxy Wi-Fi | grep -q "Enabled: Yes"; then
    echo -e "   ${GREEN}✅ 系统SOCKS代理已启用${NC}"
else
    echo -e "   ${YELLOW}⚠️  系统SOCKS代理未启用${NC}"
fi

# 5. 检查开发工具配置
echo -e "${BLUE}5. 开发工具配置检查${NC}"
git_proxy=$(git config --global --get http.proxy 2>/dev/null || echo "未设置")
if [ "$git_proxy" != "未设置" ]; then
    echo -e "   ${GREEN}✅ Git代理已配置: $git_proxy${NC}"
else
    echo -e "   ${YELLOW}⚠️  Git代理未配置${NC}"
fi

npm_proxy=$(npm config get proxy 2>/dev/null || echo "未设置")
if [ "$npm_proxy" != "未设置" ] && [ "$npm_proxy" != "null" ]; then
    echo -e "   ${GREEN}✅ npm代理已配置: $npm_proxy${NC}"
else
    echo -e "   ${YELLOW}⚠️  npm代理未配置${NC}"
fi

# 6. 实用性测试
echo -e "${BLUE}6. 实用性测试${NC}"

# 测试浏览器代理（通过检查Chrome进程连接）
if lsof -i :1080 | grep -q Chrome || lsof -i :1086 | grep -q Chrome; then
    echo -e "   ${GREEN}✅ 浏览器正在使用代理${NC}"
else
    echo -e "   ${YELLOW}⚠️  浏览器可能未使用代理${NC}"
fi

# 检查v2ray进程
v2ray_count=$(ps aux | grep v2ray | grep -v grep | wc -l)
if [ "$v2ray_count" -gt 0 ]; then
    echo -e "   ${GREEN}✅ v2ray进程正在运行 ($v2ray_count 个)${NC}"
else
    echo -e "   ${RED}❌ v2ray进程未运行${NC}"
fi

# 7. 总体评估
echo -e "${BLUE}7. 总体评估${NC}"

score=0
total=8

# 评分标准
if [ "$functions_ok" = true ]; then score=$((score + 1)); fi
if [ "$socks_running" = true ]; then score=$((score + 1)); fi
if [ "$http_running" = true ]; then score=$((score + 1)); fi
if [ -n "$http_proxy" ]; then score=$((score + 1)); fi
if [ -n "$all_proxy" ]; then score=$((score + 1)); fi
if networksetup -getwebproxy Wi-Fi | grep -q "Enabled: Yes"; then score=$((score + 1)); fi
if [ "$git_proxy" != "未设置" ]; then score=$((score + 1)); fi
if [ "$v2ray_count" -gt 0 ]; then score=$((score + 1)); fi

percentage=$((score * 100 / total))

if [ "$percentage" -ge 80 ]; then
    echo -e "   ${GREEN}🎉 代理配置优秀 ($score/$total, $percentage%)${NC}"
    echo -e "   ${GREEN}✅ 您的代理配置基本完美，可以正常使用${NC}"
elif [ "$percentage" -ge 60 ]; then
    echo -e "   ${YELLOW}⚠️  代理配置良好 ($score/$total, $percentage%)${NC}"
    echo -e "   ${YELLOW}✅ 代理基本可用，有少量问题${NC}"
else
    echo -e "   ${RED}❌ 代理配置需要改进 ($score/$total, $percentage%)${NC}"
    echo -e "   ${RED}⚠️  存在较多问题，需要修复${NC}"
fi

echo
echo -e "${BLUE}📋 使用建议：${NC}"
echo "1. 代理函数已可用，使用 proxy_status 查看状态"
echo "2. 使用 pon/poff 快速开关代理"
echo "3. 浏览器和开发工具应该可以正常使用代理"
echo "4. 如果遇到网络问题，运行 proxy_fix 修复"

echo
echo -e "${BLUE}🎯 快速测试命令：${NC}"
echo "proxy_status  # 查看代理状态"
echo "pon          # 开启代理"
echo "poff         # 关闭代理"

echo
echo -e "${GREEN}🎉 验证完成！您的代理配置已经可以正常使用了！${NC}"
