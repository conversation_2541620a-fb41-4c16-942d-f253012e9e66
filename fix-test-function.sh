#!/bin/bash

# 修复代理测试函数
echo "🔧 修复代理测试函数..."

# 重新定义正确的proxy_test函数
proxy_test() {
  echo "🔍 测试代理连接..."
  
  # 临时清除no_proxy环境变量以确保测试准确
  local old_no_proxy="$no_proxy"
  unset no_proxy
  
  # 测试SOCKS代理
  echo -n "测试SOCKS代理: "
  socks_result=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$socks_result" ]; then
    echo "✅ 工作正常，IP: $socks_result"
  else
    echo "❌ 连接失败"
  fi
  
  # 测试HTTP代理
  echo -n "测试HTTP代理: "
  http_result=$(timeout 10 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$http_result" ]; then
    echo "✅ 工作正常，IP: $http_result"
  else
    echo "❌ 连接失败"
  fi
  
  # 测试被墙网站
  echo -n "测试Twitter访问: "
  twitter_code=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
  if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
    echo "✅ 可以访问 (HTTP $twitter_code)"
  else
    echo "⚠️  访问异常 (HTTP $twitter_code)"
  fi
  
  # 恢复no_proxy环境变量
  if [ -n "$old_no_proxy" ]; then
    export no_proxy="$old_no_proxy"
  fi
}

# 重新定义完整的代理测试
proxy_full_test() {
  echo "🔍 完整代理测试..."
  
  # 检查服务状态
  echo "1. 检查v2ray服务:"
  if lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN; then
    echo "   ✅ 代理服务器正在运行"
  else
    echo "   ❌ 代理服务器未运行"
    return 1
  fi
  
  # 检查代理函数
  echo "2. 检查代理函数:"
  if type proxy_on >/dev/null 2>&1; then
    echo "   ✅ proxy_on函数可用"
  else
    echo "   ❌ proxy_on函数不可用"
  fi
  
  if type proxy_status >/dev/null 2>&1; then
    echo "   ✅ proxy_status函数可用"
  else
    echo "   ❌ proxy_status函数不可用"
  fi
  
  # 测试代理连接
  echo "3. 测试代理连接:"
  proxy_test
  
  # 检查系统代理
  echo "4. 检查系统代理:"
  if networksetup -getwebproxy Wi-Fi | grep -q "Enabled: Yes"; then
    echo "   ✅ 系统HTTP代理已启用"
  else
    echo "   ❌ 系统HTTP代理未启用"
  fi
  
  if networksetup -getsocksfirewallproxy Wi-Fi | grep -q "Enabled: Yes"; then
    echo "   ✅ 系统SOCKS代理已启用"
  else
    echo "   ❌ 系统SOCKS代理未启用"
  fi
}

echo "✅ 测试函数已修复"
echo
echo "💡 现在可以使用："
echo "   proxy_test       # 快速测试代理连接"
echo "   proxy_full_test  # 完整测试所有功能"
