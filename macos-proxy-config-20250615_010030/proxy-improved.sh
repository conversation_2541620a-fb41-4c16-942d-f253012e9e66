#!/bin/bash

# 改进的代理管理脚本
# 修正了连接测试和SSL问题

PROXY_HOST="127.0.0.1"
SOCKS_PORT="1080"
HTTP_PORT="1086"
HTTP_PROXY="http://${PROXY_HOST}:${HTTP_PORT}"
SOCKS_PROXY="socks5://${PROXY_HOST}:${SOCKS_PORT}"

case "$1" in
    on)
        export http_proxy="$HTTP_PROXY"
        export https_proxy="$HTTP_PROXY"
        export all_proxy="$SOCKS_PROXY"
        export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
        echo "✅ 代理已开启"
        echo "   HTTP/HTTPS: $HTTP_PROXY"
        echo "   SOCKS: $SOCKS_PROXY"
        ;;
    off)
        unset http_proxy https_proxy all_proxy no_proxy
        echo "✅ 代理已关闭"
        ;;
    status)
        if [ -n "$http_proxy" ]; then
            echo "✅ 代理状态：开启"
            echo "   HTTP代理：$http_proxy"
            echo "   HTTPS代理：$https_proxy"
            echo "   SOCKS代理：$all_proxy"
            echo "   绕过规则：$no_proxy"
        else
            echo "❌ 代理状态：关闭"
        fi
        ;;
    test)
        echo "🔍 测试代理连接..."
        
        # 1. 检查代理服务器进程
        echo "1. 检查代理服务器..."
        socks_running=false
        http_running=false

        if lsof -i :$SOCKS_PORT 2>/dev/null | grep -q LISTEN; then
            socks_process=$(lsof -i :$SOCKS_PORT 2>/dev/null | grep LISTEN | awk '{print $1}' | head -1)
            echo "   ✅ SOCKS代理服务器正在运行 ($socks_process:$SOCKS_PORT)"
            socks_running=true
        else
            echo "   ❌ SOCKS代理服务器未运行 (端口$SOCKS_PORT)"
        fi

        if lsof -i :$HTTP_PORT 2>/dev/null | grep -q LISTEN; then
            http_process=$(lsof -i :$HTTP_PORT 2>/dev/null | grep LISTEN | awk '{print $1}' | head -1)
            echo "   ✅ HTTP代理服务器正在运行 ($http_process:$HTTP_PORT)"
            http_running=true
        else
            echo "   ❌ HTTP代理服务器未运行 (端口$HTTP_PORT)"
        fi

        if [ "$socks_running" = false ] && [ "$http_running" = false ]; then
            echo "   ❌ 没有代理服务器在运行"
            echo "   请启动您的代理客户端（如v2ray、Clash等）"
            exit 1
        fi

        # 2. 检查端口连通性
        echo "2. 检查端口连通性..."
        if [ "$socks_running" = true ] && nc -z $PROXY_HOST $SOCKS_PORT 2>/dev/null; then
            echo "   ✅ SOCKS端口 $SOCKS_PORT 可达"
        fi
        if [ "$http_running" = true ] && nc -z $PROXY_HOST $HTTP_PORT 2>/dev/null; then
            echo "   ✅ HTTP端口 $HTTP_PORT 可达"
        fi
        
        # 3. 测试SOCKS代理连接
        echo "3. 测试SOCKS代理连接..."
        if [ "$socks_running" = true ]; then
            # 测试Google（确保走代理）
            if timeout 5 curl -x "$SOCKS_PROXY" -s https://www.google.com > /dev/null 2>&1; then
                echo "   ✅ SOCKS代理连接正常 (Google)"
            else
                echo "   ⚠️  SOCKS代理连接失败"
            fi
        else
            echo "   ⚠️  SOCKS代理未运行，跳过测试"
        fi

        # 4. 测试HTTP代理连接
        echo "4. 测试HTTP代理连接..."
        if [ "$http_running" = true ]; then
            # 测试Google（确保走代理）
            if timeout 5 curl -x "$HTTP_PROXY" -s https://www.google.com > /dev/null 2>&1; then
                echo "   ✅ HTTP代理连接正常 (Google)"
            else
                echo "   ⚠️  HTTP代理连接失败"
            fi
        else
            echo "   ⚠️  HTTP代理未运行，跳过测试"
        fi

        # 5. 获取外网IP（使用国外IP查询服务）
        echo "5. 获取外网IP..."
        if [ "$socks_running" = true ]; then
            external_ip=$(timeout 5 curl -x "$SOCKS_PROXY" -s https://api.ipify.org 2>/dev/null)
            if [ -n "$external_ip" ]; then
                echo "   ✅ 当前外网IP: $external_ip (通过SOCKS代理)"
            else
                echo "   ⚠️  无法通过SOCKS代理获取外网IP"
            fi
        elif [ "$http_running" = true ]; then
            external_ip=$(timeout 5 curl -x "$HTTP_PROXY" -s https://api.ipify.org 2>/dev/null)
            if [ -n "$external_ip" ]; then
                echo "   ✅ 当前外网IP: $external_ip (通过HTTP代理)"
            else
                echo "   ⚠️  无法通过HTTP代理获取外网IP"
            fi
        fi
        
        echo "🎉 代理测试完成"
        ;;
    fix)
        echo "🔧 自动修复代理配置..."
        
        # 重新设置环境变量
        export http_proxy="$HTTP_PROXY"
        export https_proxy="$HTTP_PROXY"
        export all_proxy="$SOCKS_PROXY"
        export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
        
        # 重新配置Git
        git config --global http.proxy "$HTTP_PROXY"
        git config --global https.proxy "$HTTP_PROXY"
        
        # 重新配置npm
        if command -v npm &> /dev/null; then
            npm config set proxy "$HTTP_PROXY"
            npm config set https-proxy "$HTTP_PROXY"
        fi
        
        echo "✅ 代理配置已修复"
        ;;
    *)
        echo "用法: $0 {on|off|status|test|fix}"
        echo ""
        echo "命令说明："
        echo "  on     - 开启代理"
        echo "  off    - 关闭代理"
        echo "  status - 查看代理状态"
        echo "  test   - 测试代理连接"
        echo "  fix    - 修复代理配置"
        exit 1
        ;;
esac
