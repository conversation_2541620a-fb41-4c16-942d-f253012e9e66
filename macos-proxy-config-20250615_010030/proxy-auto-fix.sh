#!/bin/bash

# macOS 代理自动修复脚本
# 作者：AI Assistant
# 用途：自动修复常见的代理配置问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROXY_HOST="127.0.0.1"
PROXY_PORT="1080"
SOCKS_PROXY="socks5://${PROXY_HOST}:${PROXY_PORT}"
HTTP_PROXY="http://${PROXY_HOST}:${PROXY_PORT}"

echo -e "${BLUE}=== macOS 代理自动修复 ===${NC}"
echo "修复时间: $(date)"
echo

# 确认执行
read -p "是否继续执行自动修复？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "取消修复"
    exit 1
fi

# 1. 统一环境变量
echo -e "${BLUE}1. 统一代理环境变量${NC}"
echo "设置统一的代理环境变量..."

# 备份当前配置
backup_file="$HOME/.proxy_backup_$(date +%Y%m%d_%H%M%S)"
echo "备份当前环境变量到: $backup_file"
env | grep -i proxy > "$backup_file" 2>/dev/null || echo "# 无现有代理配置" > "$backup_file"

# 设置新的环境变量
export http_proxy="$HTTP_PROXY"
export https_proxy="$HTTP_PROXY"
export all_proxy="$SOCKS_PROXY"
export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"

echo -e "  ${GREEN}✅ 环境变量已统一设置${NC}"
echo "    http_proxy=$http_proxy"
echo "    https_proxy=$https_proxy"
echo "    all_proxy=$all_proxy"
echo

# 2. 更新 shell 配置文件
echo -e "${BLUE}2. 更新 shell 配置文件${NC}"

# 检测使用的 shell
if [[ "$SHELL" == *"zsh"* ]]; then
    SHELL_CONFIG="$HOME/.zshrc"
elif [[ "$SHELL" == *"bash"* ]]; then
    SHELL_CONFIG="$HOME/.bash_profile"
else
    SHELL_CONFIG="$HOME/.profile"
fi

echo "更新配置文件: $SHELL_CONFIG"

# 备份配置文件
cp "$SHELL_CONFIG" "${SHELL_CONFIG}.backup_$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true

# 移除旧的代理配置
sed -i '' '/# 代理设置/,/# 代理设置结束/d' "$SHELL_CONFIG" 2>/dev/null || true
sed -i '' '/export.*proxy/d' "$SHELL_CONFIG" 2>/dev/null || true

# 添加新的代理配置
cat >> "$SHELL_CONFIG" << EOF

# 代理设置
export http_proxy=$HTTP_PROXY
export https_proxy=$HTTP_PROXY
export all_proxy=$SOCKS_PROXY
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16

# 代理管理函数
proxy_on() {
    export http_proxy=$HTTP_PROXY
    export https_proxy=$HTTP_PROXY
    export all_proxy=$SOCKS_PROXY
    echo "代理已开启"
}

proxy_off() {
    unset http_proxy
    unset https_proxy
    unset all_proxy
    echo "代理已关闭"
}

proxy_status() {
    if [ -n "\$http_proxy" ]; then
        echo "代理状态：开启"
        echo "HTTP代理：\$http_proxy"
        echo "HTTPS代理：\$https_proxy"
        echo "SOCKS代理：\$all_proxy"
    else
        echo "代理状态：关闭"
    fi
}
# 代理设置结束
EOF

echo -e "  ${GREEN}✅ Shell 配置文件已更新${NC}"
echo

# 3. 配置 Git
echo -e "${BLUE}3. 配置 Git 代理${NC}"
git config --global http.proxy "$HTTP_PROXY"
git config --global https.proxy "$HTTP_PROXY"
echo -e "  ${GREEN}✅ Git 代理配置完成${NC}"
echo "    http.proxy: $(git config --global --get http.proxy)"
echo "    https.proxy: $(git config --global --get https.proxy)"
echo

# 4. 配置 npm
echo -e "${BLUE}4. 配置 npm 代理${NC}"
if command -v npm &> /dev/null; then
    npm config set proxy "$HTTP_PROXY"
    npm config set https-proxy "$HTTP_PROXY"
    echo -e "  ${GREEN}✅ npm 代理配置完成${NC}"
    echo "    proxy: $(npm config get proxy)"
    echo "    https-proxy: $(npm config get https-proxy)"
else
    echo -e "  ${YELLOW}⚠️  npm 未安装，跳过配置${NC}"
fi
echo

# 5. 配置 pip
echo -e "${BLUE}5. 配置 pip 代理${NC}"
if command -v pip3 &> /dev/null; then
    mkdir -p "$HOME/.pip"
    cat > "$HOME/.pip/pip.conf" << EOF
[global]
proxy = $HTTP_PROXY
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
               mirrors.aliyun.com
index-url = https://pypi.org/simple/
EOF
    echo -e "  ${GREEN}✅ pip 代理配置完成${NC}"
    echo "    配置文件: ~/.pip/pip.conf"
else
    echo -e "  ${YELLOW}⚠️  pip3 未安装，跳过配置${NC}"
fi
echo

# 6. 配置系统代理
echo -e "${BLUE}6. 配置系统代理${NC}"
echo "设置系统 SOCKS 代理..."

# 获取当前网络服务
network_service=$(networksetup -listallnetworkservices | grep -E "(Wi-Fi|Ethernet)" | head -1)
if [ -n "$network_service" ]; then
    networksetup -setsocksfirewallproxy "$network_service" "$PROXY_HOST" "$PROXY_PORT"
    networksetup -setsocksfirewallproxystate "$network_service" on
    echo -e "  ${GREEN}✅ 系统代理配置完成${NC}"
    echo "    网络服务: $network_service"
    echo "    代理地址: $PROXY_HOST:$PROXY_PORT"
else
    echo -e "  ${RED}❌ 未找到网络服务${NC}"
fi
echo

# 7. 创建便捷脚本
echo -e "${BLUE}7. 创建便捷管理脚本${NC}"
mkdir -p "$HOME/bin"

# 创建代理切换脚本
cat > "$HOME/bin/proxy" << 'EOF'
#!/bin/bash

PROXY_HOST="127.0.0.1"
PROXY_PORT="1080"
HTTP_PROXY="http://${PROXY_HOST}:${PROXY_PORT}"
SOCKS_PROXY="socks5://${PROXY_HOST}:${PROXY_PORT}"

case "$1" in
    on)
        export http_proxy="$HTTP_PROXY"
        export https_proxy="$HTTP_PROXY"
        export all_proxy="$SOCKS_PROXY"
        echo "代理已开启"
        ;;
    off)
        unset http_proxy
        unset https_proxy
        unset all_proxy
        echo "代理已关闭"
        ;;
    status)
        if [ -n "$http_proxy" ]; then
            echo "代理状态：开启"
            echo "HTTP代理：$http_proxy"
            echo "HTTPS代理：$https_proxy"
            echo "SOCKS代理：$all_proxy"
        else
            echo "代理状态：关闭"
        fi
        ;;
    test)
        if [ -n "$http_proxy" ]; then
            echo "测试代理连接..."
            curl -x "$SOCKS_PROXY" --max-time 10 -s http://httpbin.org/ip
        else
            echo "代理未开启"
        fi
        ;;
    *)
        echo "用法: proxy {on|off|status|test}"
        exit 1
        ;;
esac
EOF

chmod +x "$HOME/bin/proxy"
echo -e "  ${GREEN}✅ 便捷脚本创建完成${NC}"
echo "    使用方法: proxy {on|off|status|test}"
echo

# 8. 验证配置
echo -e "${BLUE}8. 验证配置${NC}"
echo "测试代理连接..."

if timeout 10 curl -x "$SOCKS_PROXY" -s --max-time 5 http://httpbin.org/ip > /dev/null 2>&1; then
    echo -e "  ${GREEN}✅ 代理连接测试成功${NC}"
else
    echo -e "  ${RED}❌ 代理连接测试失败${NC}"
    echo "    请检查代理服务器是否正在运行"
fi

echo
echo -e "${GREEN}=== 修复完成 ===${NC}"
echo "请执行以下命令使配置生效："
echo "  source $SHELL_CONFIG"
echo
echo "或者重新打开终端窗口"
echo
echo "使用 './proxy-health-check.sh' 再次检查配置状态"
