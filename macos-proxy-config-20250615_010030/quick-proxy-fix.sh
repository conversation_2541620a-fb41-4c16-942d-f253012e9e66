#!/bin/bash

# 快速代理修复脚本
echo "=== 快速代理修复 ==="

# 1. 统一环境变量
echo "1. 统一环境变量..."
export http_proxy=http://127.0.0.1:1080
export https_proxy=http://127.0.0.1:1080
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
echo "✅ 环境变量已设置"

# 2. 配置Git
echo "2. 配置Git代理..."
git config --global http.proxy http://127.0.0.1:1080
git config --global https.proxy http://127.0.0.1:1080
echo "✅ Git代理已配置"

# 3. 配置npm
echo "3. 配置npm代理..."
npm config set proxy http://127.0.0.1:1080 2>/dev/null || echo "npm未安装，跳过"
npm config set https-proxy http://127.0.0.1:1080 2>/dev/null || echo "npm未安装，跳过"
echo "✅ npm代理已配置"

# 4. 配置pip
echo "4. 配置pip代理..."
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
proxy = http://127.0.0.1:1080
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
index-url = https://pypi.org/simple/
EOF
echo "✅ pip代理已配置"

# 5. 更新shell配置
echo "5. 更新shell配置..."
SHELL_CONFIG="$HOME/.zshrc"
if [[ "$SHELL" == *"bash"* ]]; then
    SHELL_CONFIG="$HOME/.bash_profile"
fi

# 备份并更新配置
cp "$SHELL_CONFIG" "${SHELL_CONFIG}.backup_$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true

# 移除旧配置
sed -i '' '/# 代理设置/,/# 代理设置结束/d' "$SHELL_CONFIG" 2>/dev/null || true

# 添加新配置
cat >> "$SHELL_CONFIG" << 'EOF'

# 代理设置
export http_proxy=http://127.0.0.1:1080
export https_proxy=http://127.0.0.1:1080
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16

# 代理管理函数
proxy_on() {
    export http_proxy=http://127.0.0.1:1080
    export https_proxy=http://127.0.0.1:1080
    export all_proxy=socks5://127.0.0.1:1080
    echo "代理已开启"
}

proxy_off() {
    unset http_proxy
    unset https_proxy
    unset all_proxy
    echo "代理已关闭"
}

proxy_status() {
    if [ -n "$http_proxy" ]; then
        echo "代理状态：开启"
        echo "HTTP代理：$http_proxy"
        echo "HTTPS代理：$https_proxy"
        echo "SOCKS代理：$all_proxy"
    else
        echo "代理状态：关闭"
    fi
}
# 代理设置结束
EOF

echo "✅ Shell配置已更新"

# 6. 创建便捷脚本
echo "6. 创建便捷脚本..."
mkdir -p ~/bin
cat > ~/bin/proxy << 'EOF'
#!/bin/bash
case "$1" in
    on)
        export http_proxy=http://127.0.0.1:1080
        export https_proxy=http://127.0.0.1:1080
        export all_proxy=socks5://127.0.0.1:1080
        echo "代理已开启"
        ;;
    off)
        unset http_proxy https_proxy all_proxy
        echo "代理已关闭"
        ;;
    status)
        if [ -n "$http_proxy" ]; then
            echo "代理状态：开启 ($http_proxy)"
        else
            echo "代理状态：关闭"
        fi
        ;;
    test)
        curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null && echo "代理连接正常" || echo "代理连接失败"
        ;;
    *)
        echo "用法: proxy {on|off|status|test}"
        ;;
esac
EOF
chmod +x ~/bin/proxy
echo "✅ 便捷脚本已创建"

echo
echo "=== 修复完成 ==="
echo "请执行以下命令使配置生效："
echo "  source ~/.zshrc"
echo
echo "或者重新打开终端窗口"
echo
echo "使用方法："
echo "  proxy on     # 开启代理"
echo "  proxy off    # 关闭代理"
echo "  proxy status # 查看状态"
echo "  proxy test   # 测试连接"
