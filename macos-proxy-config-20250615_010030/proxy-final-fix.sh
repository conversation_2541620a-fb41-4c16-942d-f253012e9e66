#!/bin/bash

# 最终代理修复脚本 - 基于诊断结果
echo "🔧 最终代理修复方案"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}📋 诊断结果总结：${NC}"
echo "✅ SOCKS代理 (端口1080) 完全正常工作"
echo "❌ HTTP代理 (端口1086) 有连接问题"
echo "✅ 网络连接正常"
echo "✅ v2ray服务器可达 (*************)"
echo

echo -e "${BLUE}🛠️ 修复方案：优先使用SOCKS代理${NC}"

# 1. 更新环境变量 - 全部使用SOCKS代理
echo "1. 更新环境变量为SOCKS代理..."
export http_proxy=socks5://127.0.0.1:1080
export https_proxy=socks5://127.0.0.1:1080
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
echo -e "${GREEN}✅ 环境变量已设置为SOCKS代理${NC}"

# 2. 更新Shell配置
echo "2. 更新Shell配置..."
SHELL_CONFIG="$HOME/.zshrc"
if [[ "$SHELL" == *"bash"* ]]; then
    SHELL_CONFIG="$HOME/.bash_profile"
fi

# 备份配置
cp "$SHELL_CONFIG" "${SHELL_CONFIG}.backup_socks_$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true

# 移除旧配置
sed -i '' '/# 代理设置/,/# 代理设置结束/d' "$SHELL_CONFIG" 2>/dev/null || true

# 添加新配置 - 全SOCKS
cat >> "$SHELL_CONFIG" << 'EOF'

# 代理设置 - 优化为全SOCKS代理
export http_proxy=socks5://127.0.0.1:1080
export https_proxy=socks5://127.0.0.1:1080
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16

# 代理管理函数
proxy_on() {
    export http_proxy=socks5://127.0.0.1:1080
    export https_proxy=socks5://127.0.0.1:1080
    export all_proxy=socks5://127.0.0.1:1080
    export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
    echo "✅ 代理已开启 (全SOCKS模式)"
}

proxy_off() {
    unset http_proxy https_proxy all_proxy no_proxy
    echo "✅ 代理已关闭"
}

proxy_status() {
    if [ -n "$http_proxy" ]; then
        echo "✅ 代理状态：开启 (全SOCKS模式)"
        echo "   HTTP代理：$http_proxy"
        echo "   HTTPS代理：$https_proxy"
        echo "   SOCKS代理：$all_proxy"
    else
        echo "❌ 代理状态：关闭"
    fi
}

proxy_test() {
    echo "🔍 测试代理连接..."
    if [ -n "$http_proxy" ]; then
        result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
        if [ -n "$result" ]; then
            echo "✅ 代理工作正常，外网IP: $result"
        else
            echo "❌ 代理连接失败"
        fi
    else
        echo "❌ 代理未开启"
    fi
}
# 代理设置结束
EOF

echo -e "${GREEN}✅ Shell配置已更新${NC}"

# 3. 更新开发工具配置
echo "3. 更新开发工具配置..."

# Git - 某些Git版本不支持SOCKS，使用HTTP代理但通过SOCKS转发
git config --global http.proxy socks5://127.0.0.1:1080
git config --global https.proxy socks5://127.0.0.1:1080
echo -e "${GREEN}✅ Git配置完成${NC}"

# npm - 使用SOCKS代理
if command -v npm &> /dev/null; then
    npm config set proxy socks5://127.0.0.1:1080
    npm config set https-proxy socks5://127.0.0.1:1080
    echo -e "${GREEN}✅ npm配置完成${NC}"
fi

# pip - 更新为SOCKS代理
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
proxy = socks5://127.0.0.1:1080
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
index-url = https://pypi.org/simple/
EOF
echo -e "${GREEN}✅ pip配置完成${NC}"

# 4. 创建优化的便捷脚本
echo "4. 创建优化的便捷脚本..."
cat > ~/bin/proxy << 'EOF'
#!/bin/bash

case "$1" in
    on)
        export http_proxy=socks5://127.0.0.1:1080
        export https_proxy=socks5://127.0.0.1:1080
        export all_proxy=socks5://127.0.0.1:1080
        export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
        echo "✅ 代理已开启 (全SOCKS模式)"
        ;;
    off)
        unset http_proxy https_proxy all_proxy no_proxy
        echo "✅ 代理已关闭"
        ;;
    status)
        if [ -n "$http_proxy" ]; then
            echo "✅ 代理状态：开启 (全SOCKS模式)"
            echo "   代理地址：$http_proxy"
        else
            echo "❌ 代理状态：关闭"
        fi
        ;;
    test)
        echo "🔍 测试SOCKS代理连接..."
        if lsof -i :1080 | grep -q LISTEN; then
            echo "✅ SOCKS服务器正在运行"
            result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
            if [ -n "$result" ]; then
                echo "✅ 代理工作正常，外网IP: $result"
                
                # 测试被墙网站
                twitter_code=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
                if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
                    echo "✅ 可以访问Twitter (HTTP $twitter_code)"
                else
                    echo "⚠️  Twitter访问异常 (HTTP $twitter_code)"
                fi
            else
                echo "❌ 代理连接失败"
            fi
        else
            echo "❌ SOCKS服务器未运行"
        fi
        ;;
    *)
        echo "用法: proxy {on|off|status|test}"
        echo ""
        echo "命令说明："
        echo "  on     - 开启代理 (全SOCKS模式)"
        echo "  off    - 关闭代理"
        echo "  status - 查看代理状态"
        echo "  test   - 测试代理连接"
        ;;
esac
EOF

chmod +x ~/bin/proxy
echo -e "${GREEN}✅ 便捷脚本已创建${NC}"

# 5. 验证修复结果
echo -e "${BLUE}5. 验证修复结果${NC}"
echo "测试SOCKS代理连接..."
test_result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
if [ -n "$test_result" ]; then
    echo -e "${GREEN}✅ 修复成功！外网IP: $test_result${NC}"
else
    echo -e "${YELLOW}⚠️  测试失败，请检查v2ray服务${NC}"
fi

echo
echo -e "${GREEN}🎉 修复完成！${NC}"
echo
echo -e "${BLUE}📋 修复总结：${NC}"
echo "✅ 全部配置改为使用SOCKS代理 (端口1080)"
echo "✅ 环境变量已更新"
echo "✅ Git、npm、pip配置已更新"
echo "✅ 便捷管理脚本已优化"
echo
echo -e "${BLUE}🚀 使用方法：${NC}"
echo "source ~/.zshrc    # 重新加载配置"
echo "proxy test         # 测试代理连接"
echo "proxy status       # 查看代理状态"
echo
echo -e "${YELLOW}💡 说明：${NC}"
echo "现在所有代理都使用SOCKS协议，更稳定可靠"
echo "HTTP代理端口1086暂时停用，避免连接问题"
