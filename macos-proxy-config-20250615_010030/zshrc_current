# zsh配置文件 - 优化版本

# 基础环境设置
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# PATH设置
export PATH="/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:$HOME/bin"

# 如果存在conda，初始化conda
if [ -f "$HOME/anaconda3/etc/profile.d/conda.sh" ]; then
    . "$HOME/anaconda3/etc/profile.d/conda.sh"
elif [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
    . "$HOME/miniconda3/etc/profile.d/conda.sh"
fi

# Python环境信息（可选显示）
if command -v python3 &> /dev/null; then
    echo "=== 当前Python环境 ==="
    echo "Python路径: $(which python3)"
    if python3 -c "import flask" 2>/dev/null; then
        echo "Flask状态: 已安装"
    else
        echo "Flask状态: 未安装"
    fi
    echo "===================="
fi

# 代理设置 - 优化为全SOCKS代理
export http_proxy=socks5://127.0.0.1:1080
export https_proxy=socks5://127.0.0.1:1080
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16

# 代理管理函数
proxy_on() {
    export http_proxy=socks5://127.0.0.1:1080
    export https_proxy=socks5://127.0.0.1:1080
    export all_proxy=socks5://127.0.0.1:1080
    export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
    echo "✅ 代理已开启 (全SOCKS模式)"
}

proxy_off() {
    unset http_proxy https_proxy all_proxy no_proxy
    echo "✅ 代理已关闭"
}

proxy_status() {
    if [ -n "$http_proxy" ]; then
        echo "✅ 代理状态：开启 (全SOCKS模式)"
        echo "   代理地址：$http_proxy"
    else
        echo "❌ 代理状态：关闭"
    fi
}

proxy_test() {
    echo "🔍 测试SOCKS代理连接..."
    if lsof -i :1080 | grep -q LISTEN; then
        echo "✅ SOCKS服务器正在运行"
        result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
        if [ -n "$result" ]; then
            echo "✅ 代理工作正常，外网IP: $result"
        else
            echo "❌ 代理连接失败"
        fi
    else
        echo "❌ SOCKS服务器未运行"
    fi
}

# 便捷别名
alias ll='ls -la'
alias la='ls -la'
alias l='ls -l'
alias ..='cd ..'
alias ...='cd ../..'

# 代理相关别名
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'

# 确保~/bin在PATH中
if [ -d "$HOME/bin" ]; then
    export PATH="$HOME/bin:$PATH"
fi

# zsh特定设置（避免autoload错误）
if [ -n "$ZSH_VERSION" ]; then
    # 基础的zsh设置，不依赖oh-my-zsh
    setopt AUTO_CD
    setopt CORRECT
    setopt HIST_VERIFY
    
    # 简单的提示符
    PROMPT='%n@%m:%~$ '
    
    # 基础补全
    autoload -U compinit
    compinit -u 2>/dev/null || true
fi
