#!/bin/bash

case "$1" in
    on)
        export http_proxy=socks5://127.0.0.1:1080
        export https_proxy=socks5://127.0.0.1:1080
        export all_proxy=socks5://127.0.0.1:1080
        export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
        echo "✅ 代理已开启 (全SOCKS模式)"
        ;;
    off)
        unset http_proxy https_proxy all_proxy no_proxy
        echo "✅ 代理已关闭"
        ;;
    status)
        if [ -n "$http_proxy" ]; then
            echo "✅ 代理状态：开启 (全SOCKS模式)"
            echo "   代理地址：$http_proxy"
        else
            echo "❌ 代理状态：关闭"
        fi
        ;;
    test)
        echo "🔍 测试SOCKS代理连接..."
        if lsof -i :1080 | grep -q LISTEN; then
            echo "✅ SOCKS服务器正在运行"
            result=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
            if [ -n "$result" ]; then
                echo "✅ 代理工作正常，外网IP: $result"
                
                # 测试被墙网站
                twitter_code=$(curl -x socks5://127.0.0.1:1080 --max-time 10 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
                if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
                    echo "✅ 可以访问Twitter (HTTP $twitter_code)"
                else
                    echo "⚠️  Twitter访问异常 (HTTP $twitter_code)"
                fi
            else
                echo "❌ 代理连接失败"
            fi
        else
            echo "❌ SOCKS服务器未运行"
        fi
        ;;
    *)
        echo "用法: proxy {on|off|status|test}"
        echo ""
        echo "命令说明："
        echo "  on     - 开启代理 (全SOCKS模式)"
        echo "  off    - 关闭代理"
        echo "  status - 查看代理状态"
        echo "  test   - 测试代理连接"
        ;;
esac
