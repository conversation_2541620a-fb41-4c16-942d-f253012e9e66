#!/bin/bash

# 最终代理配置脚本 - 基于v2ray配置优化
echo "🔧 基于您的v2ray配置进行最终优化..."

# v2ray配置分析
echo "📋 v2ray配置分析:"
echo "   - SOCKS代理: 127.0.0.1:1080"
echo "   - HTTP代理: 127.0.0.1:1086"
echo "   - 路由规则: 中国IP/域名直连，其他走代理"
echo

# 1. 更新环境变量
echo "1. 设置环境变量..."
export http_proxy=http://127.0.0.1:1086
export https_proxy=http://127.0.0.1:1086
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
echo "✅ 环境变量已设置"

# 2. 更新Shell配置
echo "2. 更新Shell配置..."
SHELL_CONFIG="$HOME/.zshrc"
if [[ "$SHELL" == *"bash"* ]]; then
    SHELL_CONFIG="$HOME/.bash_profile"
fi

# 备份并更新配置
cp "$SHELL_CONFIG" "${SHELL_CONFIG}.backup_v2ray_$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true

# 移除旧配置
sed -i '' '/# 代理设置/,/# 代理设置结束/d' "$SHELL_CONFIG" 2>/dev/null || true

# 添加新配置（基于v2ray双端口）
cat >> "$SHELL_CONFIG" << 'EOF'

# 代理设置 - v2ray双端口配置
export http_proxy=http://127.0.0.1:1086
export https_proxy=http://127.0.0.1:1086
export all_proxy=socks5://127.0.0.1:1080
export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16

# 代理管理函数
proxy_on() {
    export http_proxy=http://127.0.0.1:1086
    export https_proxy=http://127.0.0.1:1086
    export all_proxy=socks5://127.0.0.1:1080
    export no_proxy=localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16
    echo "✅ 代理已开启 (HTTP:1086, SOCKS:1080)"
}

proxy_off() {
    unset http_proxy https_proxy all_proxy no_proxy
    echo "✅ 代理已关闭"
}

proxy_status() {
    if [ -n "$http_proxy" ]; then
        echo "✅ 代理状态：开启"
        echo "   HTTP代理：$http_proxy"
        echo "   HTTPS代理：$https_proxy"
        echo "   SOCKS代理：$all_proxy"
        echo "   绕过规则：$no_proxy"
    else
        echo "❌ 代理状态：关闭"
    fi
}
# 代理设置结束
EOF

echo "✅ Shell配置已更新"

# 3. 配置开发工具
echo "3. 配置开发工具..."

# Git - 使用HTTP代理
git config --global http.proxy http://127.0.0.1:1086
git config --global https.proxy http://127.0.0.1:1086
echo "✅ Git配置完成 (使用HTTP代理端口1086)"

# npm - 使用HTTP代理
if command -v npm &> /dev/null; then
    npm config set proxy http://127.0.0.1:1086
    npm config set https-proxy http://127.0.0.1:1086
    echo "✅ npm配置完成 (使用HTTP代理端口1086)"
fi

# pip - 使用HTTP代理
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
proxy = http://127.0.0.1:1086
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
               mirrors.aliyun.com
index-url = https://pypi.org/simple/
EOF
echo "✅ pip配置完成 (使用HTTP代理端口1086)"

# 4. 创建v2ray专用测试脚本
echo "4. 创建v2ray专用测试脚本..."
cat > ~/bin/v2ray-test << 'EOF'
#!/bin/bash

echo "🔍 v2ray代理测试..."

# 检查v2ray进程
if pgrep -f v2ray > /dev/null; then
    echo "✅ v2ray进程正在运行"
else
    echo "❌ v2ray进程未运行"
    exit 1
fi

# 检查端口
echo "检查端口状态:"
if lsof -i :1080 | grep -q LISTEN; then
    echo "✅ SOCKS端口1080正在监听"
else
    echo "❌ SOCKS端口1080未监听"
fi

if lsof -i :1086 | grep -q LISTEN; then
    echo "✅ HTTP端口1086正在监听"
else
    echo "❌ HTTP端口1086未监听"
fi

# 测试实际应用
echo "测试实际应用:"

# 测试curl通过HTTP代理访问Google
if timeout 10 curl -x http://127.0.0.1:1086 -s https://www.google.com > /dev/null 2>&1; then
    echo "✅ HTTP代理访问Google成功"
else
    echo "⚠️  HTTP代理访问Google失败（可能被路由规则直连）"
fi

# 测试curl通过SOCKS代理访问Google
if timeout 10 curl -x socks5://127.0.0.1:1080 -s https://www.google.com > /dev/null 2>&1; then
    echo "✅ SOCKS代理访问Google成功"
else
    echo "⚠️  SOCKS代理访问Google失败（可能被路由规则直连）"
fi

# 测试访问被墙网站（确保走代理）
echo "测试访问被墙网站:"
if timeout 10 curl -x socks5://127.0.0.1:1080 -s https://twitter.com > /dev/null 2>&1; then
    echo "✅ 可以访问Twitter（代理工作正常）"
else
    echo "⚠️  无法访问Twitter"
fi

echo "🎉 v2ray测试完成"
EOF

chmod +x ~/bin/v2ray-test
echo "✅ v2ray测试脚本已创建"

echo
echo "🎉 最终配置完成！"
echo
echo "📋 配置总结:"
echo "   - HTTP/HTTPS代理: 127.0.0.1:1086"
echo "   - SOCKS代理: 127.0.0.1:1080"
echo "   - Git、npm、pip都配置为使用HTTP代理"
echo "   - 环境变量已正确设置"
echo
echo "🚀 使用方法:"
echo "   source ~/.zshrc          # 重新加载配置"
echo "   ~/bin/v2ray-test         # 测试v2ray代理"
echo "   proxy_status             # 查看代理状态"
echo "   proxy_on/proxy_off       # 开启/关闭代理"
echo
echo "⚠️  注意: 由于v2ray路由规则，国内网站会直连，国外网站走代理"
