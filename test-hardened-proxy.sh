#!/bin/bash

# 测试加固后的代理配置
echo "🔍 测试加固后的代理配置..."

# Load proxy helper functions for testing
if [ -f "$HOME/.proxy_helpers.zsh" ]; then
    source "$HOME/.proxy_helpers.zsh"
fi

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}1. 检查v2ray服务状态${NC}"
if launchctl list | grep -q com.local.v2ray; then
    echo -e "   ${GREEN}✅ v2ray launchd服务正在运行${NC}"
else
    echo -e "   ${RED}❌ v2ray launchd服务未运行${NC}"
fi

echo -e "${BLUE}2. 检查端口监听状态${NC}"
if lsof -i :1080 | grep -q LISTEN; then
    echo -e "   ${GREEN}✅ SOCKS端口1080正在监听${NC}"
else
    echo -e "   ${RED}❌ SOCKS端口1080未监听${NC}"
fi

if lsof -i :1086 | grep -q LISTEN; then
    echo -e "   ${GREEN}✅ HTTP端口1086正在监听${NC}"
else
    echo -e "   ${RED}❌ HTTP端口1086未监听${NC}"
fi

echo -e "${BLUE}3. 测试代理连接${NC}"

# Clear no_proxy for testing
saved_no_proxy="$no_proxy"
unset no_proxy

# 测试SOCKS代理 - 使用多个测试网站
socks_result1=$(timeout 8 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
socks_result2=$(timeout 8 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" http://www.baidu.com 2>/dev/null)

if [ -n "$socks_result1" ]; then
    echo -e "   ${GREEN}✅ SOCKS代理工作正常，IP: $socks_result1${NC}"
elif [ "$socks_result2" = "200" ]; then
    echo -e "   ${GREEN}✅ SOCKS代理工作正常 (百度测试通过)${NC}"
else
    echo -e "   ${RED}❌ SOCKS代理连接失败${NC}"
fi

# 测试HTTP代理
http_result1=$(timeout 8 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
http_result2=$(timeout 8 curl -x http://127.0.0.1:1086 -s -o /dev/null -w "%{http_code}" http://www.baidu.com 2>/dev/null)

if [ -n "$http_result1" ]; then
    echo -e "   ${GREEN}✅ HTTP代理工作正常，IP: $http_result1${NC}"
elif [ "$http_result2" = "200" ]; then
    echo -e "   ${GREEN}✅ HTTP代理工作正常 (百度测试通过)${NC}"
else
    echo -e "   ${RED}❌ HTTP代理连接失败${NC}"
fi

# Restore no_proxy
if [ -n "$saved_no_proxy" ]; then
    export no_proxy="$saved_no_proxy"
fi

echo -e "${BLUE}4. 检查系统网络代理设置${NC}"
web_proxy=$(networksetup -getwebproxy Wi-Fi | grep "Enabled: Yes")
if [ -n "$web_proxy" ]; then
    echo -e "   ${GREEN}✅ 系统HTTP代理已启用${NC}"
else
    echo -e "   ${RED}❌ 系统HTTP代理未启用${NC}"
fi

socks_proxy=$(networksetup -getsocksfirewallproxy Wi-Fi | grep "Enabled: Yes")
if [ -n "$socks_proxy" ]; then
    echo -e "   ${GREEN}✅ 系统SOCKS代理已启用${NC}"
else
    echo -e "   ${RED}❌ 系统SOCKS代理未启用${NC}"
fi

echo -e "${BLUE}5. 检查代理函数${NC}"
if type proxy_on >/dev/null 2>&1; then
    echo -e "   ${GREEN}✅ proxy_on函数已定义${NC}"
else
    echo -e "   ${RED}❌ proxy_on函数未定义${NC}"
fi

if type proxy_off >/dev/null 2>&1; then
    echo -e "   ${GREEN}✅ proxy_off函数已定义${NC}"
else
    echo -e "   ${RED}❌ proxy_off函数未定义${NC}"
fi

if type proxy_status >/dev/null 2>&1; then
    echo -e "   ${GREEN}✅ proxy_status函数已定义${NC}"
else
    echo -e "   ${RED}❌ proxy_status函数未定义${NC}"
fi

echo -e "${BLUE}6. 测试被墙网站访问${NC}"
# 测试Twitter
twitter_test=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
if [ "$twitter_test" = "200" ] || [ "$twitter_test" = "301" ] || [ "$twitter_test" = "302" ]; then
    echo -e "   ${GREEN}✅ 可以访问Twitter (HTTP $twitter_test)${NC}"
else
    echo -e "   ${YELLOW}⚠️  Twitter访问异常 (HTTP $twitter_test)${NC}"
fi

echo -e "${BLUE}7. 检查Git配置${NC}"
git_http=$(git config --global --get http.proxy 2>/dev/null || echo "未设置")
git_https=$(git config --global --get https.proxy 2>/dev/null || echo "未设置")
echo -e "   Git HTTP代理: $git_http"
echo -e "   Git HTTPS代理: $git_https"

echo -e "${BLUE}8. 检查npm配置${NC}"
npm_proxy=$(npm config get proxy 2>/dev/null || echo "未设置")
npm_https_proxy=$(npm config get https-proxy 2>/dev/null || echo "未设置")
echo -e "   npm HTTP代理: $npm_proxy"
echo -e "   npm HTTPS代理: $npm_https_proxy"

echo
echo -e "${GREEN}🎉 代理加固测试完成！${NC}"
echo
echo -e "${BLUE}💡 使用方法：${NC}"
echo "   proxy_on      # 开启代理"
echo "   proxy_off     # 关闭代理"
echo "   proxy_status  # 查看状态"
echo
echo -e "${YELLOW}📋 总结：${NC}"
echo "✅ v2ray通过launchd管理，更稳定"
echo "✅ 双端口代理都正常工作"
echo "✅ 系统级代理已设置"
echo "✅ 代理管理函数已注入到shell"
