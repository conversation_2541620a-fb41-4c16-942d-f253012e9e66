#!/usr/bin/env bash
# macOS proxy hardening script for Augment agent — v2.2 (2025-06-15)
# -----------------------------------------------------------------
# • Ensures single v2ray via launchd
# • Unifies SOCKS :1080 + HTTP :1086 (both must exist in v2ray config)
# • Creates ~/.proxy_helpers.zsh with proxy_on/off/status
# • Auto-sources helper in zsh (via ~/.zshenv) *and* bash (via ~/.bash_profile & ~/.bashrc)
# • Applies macOS Wi-Fi system proxy
# • Provides health-check messages at the end
# Usage:  bash macos_proxy_hardening.sh

set -euo pipefail

### ————— configurable constants ————— ###
SOCKS_PORT=1080            # local socks port
HTTP_PORT=1086             # local http proxy port
V2RAY_BIN="$(command -v v2ray || echo /usr/local/bin/v2ray)"
V2RAY_CONFIG="$HOME/.config/v2ray/config.json"
PROXY_HELPER="$HOME/.proxy_helpers.zsh"
PLIST_PATH="$HOME/Library/LaunchAgents/com.local.v2ray.plist"

### 0. sanity checks ################################################
if [ ! -x "$V2RAY_BIN" ]; then
  echo "❌  找不到 v2ray 二进制：$V2RAY_BIN" >&2; exit 1
fi

# Check if config exists, if not try to find and copy it
if [ ! -f "$V2RAY_CONFIG" ]; then
  echo "📋 v2ray配置文件不存在，尝试查找..."
  if [ -f "$HOME/.V2rayU/config.json" ]; then
    echo "📋 找到配置文件，复制到标准位置..."
    mkdir -p "$(dirname "$V2RAY_CONFIG")"
    cp "$HOME/.V2rayU/config.json" "$V2RAY_CONFIG"
  else
    echo "❌  找不到 v2ray 配置文件" >&2; exit 1
  fi
fi

# Check for both string and numeric port formats
if ! grep -q "\"port\"[[:space:]]*:[[:space:]]*\"${HTTP_PORT}\"" "$V2RAY_CONFIG" && \
   ! grep -q "\"port\"[[:space:]]*:[[:space:]]*${HTTP_PORT}" "$V2RAY_CONFIG"; then
  echo "❌  v2ray config 缺少 HTTP inbound on ${HTTP_PORT}" >&2; exit 1
fi
if ! grep -q "\"port\"[[:space:]]*:[[:space:]]*\"${SOCKS_PORT}\"" "$V2RAY_CONFIG" && \
   ! grep -q "\"port\"[[:space:]]*:[[:space:]]*${SOCKS_PORT}" "$V2RAY_CONFIG"; then
  echo "❌  v2ray config 缺少 SOCKS inbound on ${SOCKS_PORT}" >&2; exit 1
fi

### 1. restart single v2ray via launchd #############################
echo "🧹 清理所有v2ray进程..."
pkill -f "v2ray run" || true
sleep 2  # Wait for processes to terminate

cat > "$PLIST_PATH" <<PLIST
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>Label</key>                <string>com.local.v2ray</string>
  <key>ProgramArguments</key>
  <array>
    <string>${V2RAY_BIN}</string>
    <string>run</string>
    <string>-c</string>
    <string>${V2RAY_CONFIG}</string>
  </array>
  <key>RunAtLoad</key>            <true/>
  <key>KeepAlive</key>            <true/>
  <key>StandardOutPath</key>      <string>/tmp/v2ray.log</string>
  <key>StandardErrorPath</key>    <string>/tmp/v2ray.err</string>
</dict>
</plist>
PLIST

launchctl bootout gui/$(id -u) "$PLIST_PATH" 2>/dev/null || true
launchctl bootstrap gui/$(id -u) "$PLIST_PATH"

### 2. write proxy helper ###########################################
cat > "$PROXY_HELPER" <<EOF
# Generated by macos_proxy_hardening.sh (v2.2)
export http_proxy="http://127.0.0.1:${HTTP_PORT}"
export https_proxy="http://127.0.0.1:${HTTP_PORT}"
export all_proxy="socks5://127.0.0.1:${SOCKS_PORT}"

proxy_on() {
  export http_proxy="http://127.0.0.1:${HTTP_PORT}"
  export https_proxy="http://127.0.0.1:${HTTP_PORT}"
  export all_proxy="socks5://127.0.0.1:${SOCKS_PORT}"
  git config --global http.proxy  "http://127.0.0.1:${HTTP_PORT}" 2>/dev/null || true
  git config --global https.proxy "http://127.0.0.1:${HTTP_PORT}" 2>/dev/null || true
  npm config set proxy       "http://127.0.0.1:${HTTP_PORT}"      >/dev/null 2>&1 || true
  npm config set https-proxy "http://127.0.0.1:${HTTP_PORT}"      >/dev/null 2>&1 || true
  mkdir -p "$HOME/.pip"
  cat > "$HOME/.pip/pip.conf" <<PIP
[global]
proxy = http://127.0.0.1:${HTTP_PORT}
PIP
}

proxy_off() {
  unset http_proxy https_proxy all_proxy
  git config --global --unset http.proxy  2>/dev/null || true
  git config --global --unset https.proxy 2>/dev/null || true
  npm config delete proxy       >/dev/null 2>&1 || true
  npm config delete https-proxy >/dev/null 2>&1 || true
  rm -f "$HOME/.pip/pip.conf"
}

proxy_status() {
  printf "%s\n" "http_proxy=${http_proxy-}" "https_proxy=${https_proxy-}" "all_proxy=${all_proxy-}"
}
EOF
chmod 644 "$PROXY_HELPER"

### 3. source helper in zsh & bash ##################################
for rc in "$HOME/.zshenv" "$HOME/.bash_profile" "$HOME/.bashrc"; do
  [ -f "$rc" ] || touch "$rc"
  grep -q "source.*proxy_helpers.zsh" "$rc" || echo "source \"$PROXY_HELPER\"" >> "$rc"
done

### 4. apply macOS system proxy (Wi-Fi) #############################
networksetup -setwebproxy          "Wi-Fi" 127.0.0.1 "$HTTP_PORT"
networksetup -setsecurewebproxy    "Wi-Fi" 127.0.0.1 "$HTTP_PORT"
networksetup -setsocksfirewallproxy "Wi-Fi" 127.0.0.1 "$SOCKS_PORT"
networksetup -setwebproxystate         "Wi-Fi" on
networksetup -setsecurewebproxystate   "Wi-Fi" on
networksetup -setsocksfirewallproxystate "Wi-Fi" on

### 5. health checks ################################################
echo "⏳ 等待 v2ray 启动..."
sleep 3  # Give v2ray time to start
health_ok=true

if lsof -PiTCP:${HTTP_PORT} -sTCP:LISTEN -n | grep -q v2ray; then
  echo "✅  v2ray HTTP ${HTTP_PORT} 正在监听"; else echo "❌  v2ray HTTP ${HTTP_PORT} 未监听"; health_ok=false; fi
if lsof -PiTCP:${SOCKS_PORT} -sTCP:LISTEN -n | grep -q v2ray; then
  echo "✅  v2ray SOCKS ${SOCKS_PORT} 正在监听"; else echo "❌  v2ray SOCKS ${SOCKS_PORT} 未监听"; health_ok=false; fi

printf "\n%s\n" "✅ Proxy hardening v2.2 完成。请执行:  'exec zsh'  或  'source ~/.bash_profile'  以加载 proxy_on/off 函数。"

$health_ok || exit 1
