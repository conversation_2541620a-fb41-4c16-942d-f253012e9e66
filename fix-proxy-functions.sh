#!/bin/bash

# 修复代理函数问题
echo "🔧 修复代理函数配置..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}1. 备份当前.zshrc${NC}"
cp ~/.zshrc ~/.zshrc.backup_$(date +%Y%m%d_%H%M%S)
echo -e "${GREEN}✅ 备份完成${NC}"

echo -e "${BLUE}2. 清理重复的代理配置块${NC}"
# 移除所有代理相关的配置块
sed -i '' '/### <<< proxy-block-BEGIN/,/### >>> proxy-block-END/d' ~/.zshrc
sed -i '' '/# 代理设置/,/# 代理设置结束/d' ~/.zshrc

echo -e "${GREEN}✅ 清理完成${NC}"

echo -e "${BLUE}3. 添加统一的代理配置${NC}"
cat >> ~/.zshrc << 'EOF'

### <<< proxy-block-BEGIN
# 代理环境变量
export http_proxy="http://127.0.0.1:1086"
export https_proxy="http://127.0.0.1:1086"
export all_proxy="socks5://127.0.0.1:1080"
export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"

# 代理管理函数
proxy_on() {
  export http_proxy="http://127.0.0.1:1086"
  export https_proxy="http://127.0.0.1:1086"
  export all_proxy="socks5://127.0.0.1:1080"
  export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
  
  # 配置开发工具
  git config --global http.proxy "http://127.0.0.1:1086"
  git config --global https.proxy "http://127.0.0.1:1086"
  npm config set proxy "http://127.0.0.1:1086" >/dev/null 2>&1
  npm config set https-proxy "http://127.0.0.1:1086" >/dev/null 2>&1
  
  # 配置pip
  mkdir -p ~/.pip
  cat > ~/.pip/pip.conf <<PIP
[global]
proxy = http://127.0.0.1:1086
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
PIP
  
  echo "✅ 代理已开启 (HTTP:1086, SOCKS:1080)"
}

proxy_off() {
  unset http_proxy https_proxy all_proxy no_proxy
  git config --global --unset http.proxy 2>/dev/null || true
  git config --global --unset https.proxy 2>/dev/null || true
  npm config delete proxy >/dev/null 2>&1 || true
  npm config delete https-proxy >/dev/null 2>&1 || true
  rm -f ~/.pip/pip.conf
  echo "✅ 代理已关闭"
}

proxy_status() {
  echo "=== 代理状态 ==="
  echo "http_proxy=${http_proxy:-未设置}"
  echo "https_proxy=${https_proxy:-未设置}"
  echo "all_proxy=${all_proxy:-未设置}"
  echo "no_proxy=${no_proxy:-未设置}"
}

proxy_test() {
  echo "🔍 测试代理连接..."
  
  # 检查服务器状态
  if lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN; then
    echo "✅ 代理服务器正在运行"
  else
    echo "❌ 代理服务器未运行"
    return 1
  fi
  
  # 测试SOCKS代理
  socks_result=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$socks_result" ]; then
    echo "✅ SOCKS代理工作正常，IP: $socks_result"
  else
    echo "❌ SOCKS代理连接失败"
  fi
  
  # 测试HTTP代理
  http_result=$(timeout 10 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$http_result" ]; then
    echo "✅ HTTP代理工作正常，IP: $http_result"
  else
    echo "❌ HTTP代理连接失败"
  fi
  
  # 测试被墙网站
  twitter_code=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
  if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
    echo "✅ 可以访问Twitter (HTTP $twitter_code)"
  else
    echo "⚠️  Twitter访问异常 (HTTP $twitter_code)"
  fi
}

# 便捷别名
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'
### >>> proxy-block-END
EOF

echo -e "${GREEN}✅ 代理配置已添加${NC}"

echo -e "${BLUE}4. 重新加载配置${NC}"
source ~/.zshrc

echo -e "${BLUE}5. 测试代理函数${NC}"
if type proxy_on >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_on函数可用${NC}"
else
    echo -e "${RED}❌ proxy_on函数不可用${NC}"
fi

if type proxy_status >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_status函数可用${NC}"
else
    echo -e "${RED}❌ proxy_status函数不可用${NC}"
fi

echo -e "${GREEN}🎉 修复完成！${NC}"
echo
echo -e "${BLUE}💡 现在可以使用：${NC}"
echo "   proxy_on      # 开启代理"
echo "   proxy_off     # 关闭代理"
echo "   proxy_status  # 查看状态"
echo "   proxy_test    # 测试连接"
echo "   pon/poff      # 快捷别名"
