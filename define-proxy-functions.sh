#!/bin/bash

# 直接定义代理函数
echo "🔧 定义代理函数..."

# 定义proxy_on函数
proxy_on() {
  export http_proxy="http://127.0.0.1:1086"
  export https_proxy="http://127.0.0.1:1086"
  export all_proxy="socks5://127.0.0.1:1080"
  export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
  echo "✅ 代理已开启 (HTTP:1086, SOCKS:1080)"
}

# 定义proxy_off函数
proxy_off() {
  unset http_proxy https_proxy all_proxy no_proxy
  echo "✅ 代理已关闭"
}

# 定义proxy_status函数
proxy_status() {
  echo "=== 代理状态 ==="
  echo "http_proxy=${http_proxy:-未设置}"
  echo "https_proxy=${https_proxy:-未设置}"
  echo "all_proxy=${all_proxy:-未设置}"
  echo "no_proxy=${no_proxy:-未设置}"
}

# 定义proxy_test函数
proxy_test() {
  echo "🔍 测试代理连接..."
  
  # 测试SOCKS代理
  socks_result=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$socks_result" ]; then
    echo "✅ SOCKS代理工作正常，IP: $socks_result"
  else
    echo "❌ SOCKS代理连接失败"
  fi
  
  # 测试HTTP代理
  http_result=$(timeout 10 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
  if [ -n "$http_result" ]; then
    echo "✅ HTTP代理工作正常，IP: $http_result"
  else
    echo "❌ HTTP代理连接失败"
  fi
}

echo "✅ 代理函数已定义"
echo
echo "💡 现在可以使用："
echo "   proxy_on      # 开启代理"
echo "   proxy_off     # 关闭代理"
echo "   proxy_status  # 查看状态"
echo "   proxy_test    # 测试连接"
