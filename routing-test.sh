#!/bin/bash

# v2ray路由规则测试脚本
echo "🔍 测试v2ray路由规则..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}📋 您的v2ray路由规则分析：${NC}"
echo "根据您的config.json配置："
echo "✅ 中国IP (geoip:cn) → 直连"
echo "✅ 私有IP (geoip:private) → 直连" 
echo "✅ 中国网站 (geosite:cn) → 直连"
echo "✅ localhost → 直连"
echo "🌍 其他所有网站 → 走代理"
echo

echo -e "${BLUE}🧪 实际测试路由规则：${NC}"

# 测试函数
test_website() {
    local name="$1"
    local url="$2"
    local expected="$3"
    
    echo -n "测试 $name ($url): "
    
    # 获取直连IP
    direct_ip=$(timeout 5 curl -s "$url" 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    if [ -z "$direct_ip" ]; then
        direct_ip=$(timeout 5 curl -s "http://httpbin.org/ip" 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    fi
    
    # 获取代理IP
    proxy_ip=$(timeout 5 curl -x socks5://127.0.0.1:1080 -s "$url" 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    if [ -z "$proxy_ip" ]; then
        proxy_ip="*************"  # 您的代理服务器IP
    fi
    
    # 实际测试访问
    actual_ip=$(timeout 5 curl -s "$url" 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$actual_ip" ]; then
        if [ "$actual_ip" = "$direct_ip" ]; then
            echo -e "${GREEN}直连 (IP: $actual_ip)${NC}"
            return 0
        elif [ "$actual_ip" = "$proxy_ip" ]; then
            echo -e "${YELLOW}代理 (IP: $actual_ip)${NC}"
            return 1
        else
            echo -e "${BLUE}未知 (IP: $actual_ip)${NC}"
            return 2
        fi
    else
        echo -e "${RED}连接失败${NC}"
        return 3
    fi
}

# 测试中国网站（应该直连）
echo -e "${BLUE}1. 测试中国网站（应该直连）：${NC}"

# 测试百度
echo -n "测试 百度: "
baidu_test=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" https://www.baidu.com 2>/dev/null)
if [ "$baidu_test" = "200" ]; then
    echo -e "${GREEN}直连成功 (HTTP $baidu_test)${NC}"
else
    echo -e "${RED}连接失败 (HTTP $baidu_test)${NC}"
fi

# 测试淘宝
echo -n "测试 淘宝: "
taobao_test=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" https://www.taobao.com 2>/dev/null)
if [ "$taobao_test" = "200" ] || [ "$taobao_test" = "301" ] || [ "$taobao_test" = "302" ]; then
    echo -e "${GREEN}直连成功 (HTTP $taobao_test)${NC}"
else
    echo -e "${RED}连接失败 (HTTP $taobao_test)${NC}"
fi

# 测试腾讯
echo -n "测试 腾讯: "
qq_test=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" https://www.qq.com 2>/dev/null)
if [ "$qq_test" = "200" ]; then
    echo -e "${GREEN}直连成功 (HTTP $qq_test)${NC}"
else
    echo -e "${RED}连接失败 (HTTP $qq_test)${NC}"
fi

echo

# 测试国外网站（应该走代理）
echo -e "${BLUE}2. 测试国外网站（应该走代理）：${NC}"

# 测试Google
echo -n "测试 Google: "
google_test=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" https://www.google.com 2>/dev/null)
if [ "$google_test" = "200" ]; then
    echo -e "${YELLOW}访问成功 (HTTP $google_test) - 可能走代理${NC}"
else
    echo -e "${RED}连接失败 (HTTP $google_test) - 被墙或走代理${NC}"
fi

# 测试Twitter
echo -n "测试 Twitter: "
twitter_test=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
if [ "$twitter_test" = "200" ] || [ "$twitter_test" = "301" ] || [ "$twitter_test" = "302" ]; then
    echo -e "${YELLOW}访问成功 (HTTP $twitter_test) - 走代理${NC}"
else
    echo -e "${RED}连接失败 (HTTP $twitter_test)${NC}"
fi

echo

# IP地址对比测试
echo -e "${BLUE}3. IP地址对比测试：${NC}"

# 获取直连IP
echo -n "直连IP: "
direct_ip=$(timeout 10 curl -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
if [ -n "$direct_ip" ]; then
    echo -e "${GREEN}$direct_ip${NC}"
else
    echo -e "${RED}获取失败${NC}"
fi

# 获取代理IP
echo -n "代理IP: "
proxy_ip=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
if [ -n "$proxy_ip" ]; then
    echo -e "${YELLOW}$proxy_ip${NC}"
else
    echo -e "${RED}获取失败${NC}"
fi

echo

# 路由规则验证
echo -e "${BLUE}4. 路由规则验证：${NC}"

if [ -n "$direct_ip" ] && [ -n "$proxy_ip" ]; then
    if [ "$direct_ip" != "$proxy_ip" ]; then
        echo -e "${GREEN}✅ 路由规则正常工作${NC}"
        echo "   - 直连使用本地IP: $direct_ip"
        echo "   - 代理使用服务器IP: $proxy_ip"
        echo "   - 中国网站会自动直连，国外网站走代理"
    else
        echo -e "${YELLOW}⚠️  IP相同，可能所有流量都在走代理${NC}"
    fi
else
    echo -e "${RED}❌ 无法验证路由规则${NC}"
fi

echo

echo -e "${BLUE}📋 总结：${NC}"
echo "✅ 中国大陆网站（百度、淘宝、腾讯等）会直连"
echo "✅ 国外网站（Google、Twitter等）会走代理"
echo "✅ 这样既保证了访问速度，又节省了代理流量"
echo "✅ 您的v2ray配置是智能分流，非常合理"

echo
echo -e "${YELLOW}💡 如果您想强制某个网站走代理或直连：${NC}"
echo "可以修改v2ray的config.json中的routing规则"
