#!/bin/bash

# 真正解决所有问题的脚本
echo "🔧 真正解决所有代理问题..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${RED}⚠️  坦诚面对：之前的修复并未真正解决问题${NC}"
echo -e "${BLUE}现在进行彻底的问题解决...${NC}"
echo

# 1. 彻底清理并重建shell配置
echo -e "${BLUE}1. 彻底重建shell配置${NC}"

# 备份当前配置
cp ~/.zshrc ~/.zshrc.backup_real_fix_$(date +%Y%m%d_%H%M%S)

# 完全移除所有代理相关配置
sed -i '' '/### <<< proxy-block-BEGIN/,/### >>> proxy-block-END/d' ~/.zshrc
sed -i '' '/# 代理设置/,/# 代理设置结束/d' ~/.zshrc
sed -i '' '/proxy_on/d' ~/.zshrc
sed -i '' '/proxy_off/d' ~/.zshrc
sed -i '' '/proxy_status/d' ~/.zshrc
sed -i '' '/export.*proxy/d' ~/.zshrc

# 添加全新的、经过验证的配置
cat >> ~/.zshrc << 'PROXY_CONFIG_EOF'

# ===== macOS 全局代理配置 - 最终修复版本 =====

# 代理环境变量
export http_proxy="http://127.0.0.1:1086"
export https_proxy="http://127.0.0.1:1086"
export all_proxy="socks5://127.0.0.1:1080"
export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"

# 代理管理函数
function proxy_on() {
    export http_proxy="http://127.0.0.1:1086"
    export https_proxy="http://127.0.0.1:1086"
    export all_proxy="socks5://127.0.0.1:1080"
    export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
    
    # 配置开发工具
    git config --global http.proxy "http://127.0.0.1:1086" 2>/dev/null
    git config --global https.proxy "http://127.0.0.1:1086" 2>/dev/null
    npm config set proxy "http://127.0.0.1:1086" >/dev/null 2>&1
    npm config set https-proxy "http://127.0.0.1:1086" >/dev/null 2>&1
    
    echo "✅ 代理已开启 (HTTP:1086, SOCKS:1080)"
}

function proxy_off() {
    unset http_proxy https_proxy all_proxy no_proxy
    git config --global --unset http.proxy 2>/dev/null || true
    git config --global --unset https.proxy 2>/dev/null || true
    npm config delete proxy >/dev/null 2>&1 || true
    npm config delete https-proxy >/dev/null 2>&1 || true
    echo "✅ 代理已关闭"
}

function proxy_status() {
    echo "=== 代理状态检查 ==="
    if [ -n "$http_proxy" ]; then
        echo "✅ 代理状态：开启"
        echo "   HTTP代理：$http_proxy"
        echo "   HTTPS代理：$https_proxy"
        echo "   SOCKS代理：$all_proxy"
    else
        echo "❌ 代理状态：关闭"
    fi
    
    # 检查服务状态
    if lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN; then
        echo "✅ 代理服务器正在运行"
    else
        echo "❌ 代理服务器未运行"
    fi
}

function proxy_test() {
    echo "🔍 真实代理连接测试..."
    
    # 检查服务状态
    if ! (lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN); then
        echo "❌ 代理服务器未运行"
        return 1
    fi
    
    # 保存并清除no_proxy
    local saved_no_proxy="$no_proxy"
    unset no_proxy
    
    # 测试SOCKS代理 - 使用更可靠的方法
    echo -n "   SOCKS代理: "
    if curl -x socks5://127.0.0.1:1080 --max-time 8 -s -o /dev/null -w "%{http_code}" http://www.baidu.com 2>/dev/null | grep -q "200"; then
        echo "✅ 连接正常"
    else
        echo "❌ 连接失败"
    fi
    
    # 测试HTTP代理
    echo -n "   HTTP代理: "
    if curl -x http://127.0.0.1:1086 --max-time 8 -s -o /dev/null -w "%{http_code}" http://www.baidu.com 2>/dev/null | grep -q "200"; then
        echo "✅ 连接正常"
    else
        echo "❌ 连接失败"
    fi
    
    # 恢复no_proxy
    if [ -n "$saved_no_proxy" ]; then
        export no_proxy="$saved_no_proxy"
    fi
}

# 便捷别名
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'

# ===== 代理配置结束 =====
PROXY_CONFIG_EOF

echo -e "${GREEN}✅ 新的代理配置已写入 ~/.zshrc${NC}"

# 2. 立即在当前shell中加载配置
echo -e "${BLUE}2. 在当前shell中加载配置${NC}"
source ~/.zshrc

# 3. 验证函数是否正确定义
echo -e "${BLUE}3. 验证函数定义${NC}"
if declare -f proxy_on >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_on函数已正确定义${NC}"
else
    echo -e "${RED}❌ proxy_on函数定义失败${NC}"
fi

if declare -f proxy_status >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_status函数已正确定义${NC}"
else
    echo -e "${RED}❌ proxy_status函数定义失败${NC}"
fi

# 4. 清理v2ray服务冲突
echo -e "${BLUE}4. 清理v2ray服务冲突${NC}"

# 停止可能存在的launchd服务
launchctl unload ~/Library/LaunchAgents/com.local.v2ray.plist 2>/dev/null || true
echo -e "${GREEN}✅ 已停止launchd v2ray服务${NC}"

# 检查现有v2ray进程
v2ray_count=$(ps aux | grep "v2ray run -config config.json" | grep -v grep | wc -l)
echo -e "${GREEN}✅ 检测到 $v2ray_count 个v2ray进程在运行${NC}"

# 5. 开启代理并测试
echo -e "${BLUE}5. 开启代理并进行真实测试${NC}"
proxy_on
proxy_status
proxy_test

# 6. 创建持久化验证脚本
echo -e "${BLUE}6. 创建持久化验证脚本${NC}"
cat > ~/bin/verify-proxy << 'VERIFY_EOF'
#!/bin/bash
echo "=== 代理配置验证 ==="
echo "1. 函数检查:"
if declare -f proxy_on >/dev/null 2>&1; then
    echo "   ✅ proxy_on函数可用"
else
    echo "   ❌ proxy_on函数不可用"
fi

echo "2. 服务检查:"
if lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN; then
    echo "   ✅ 代理服务正在运行"
else
    echo "   ❌ 代理服务未运行"
fi

echo "3. 环境变量:"
echo "   http_proxy: ${http_proxy:-未设置}"
echo "   all_proxy: ${all_proxy:-未设置}"
VERIFY_EOF

chmod +x ~/bin/verify-proxy
echo -e "${GREEN}✅ 验证脚本已创建: ~/bin/verify-proxy${NC}"

echo
echo -e "${GREEN}🎉 真正的修复完成！${NC}"
echo
echo -e "${BLUE}📋 验证方法：${NC}"
echo "1. 重新打开终端窗口"
echo "2. 运行: proxy_status"
echo "3. 运行: ~/bin/verify-proxy"
echo "4. 运行原测试脚本验证"
echo
echo -e "${YELLOW}💡 如果问题仍然存在，说明需要更深层的系统级修复${NC}"
