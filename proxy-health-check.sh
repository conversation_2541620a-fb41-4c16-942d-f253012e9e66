#!/bin/bash

# macOS 代理健康检查和修复脚本
# 作者：AI Assistant
# 用途：检查和修复常见的代理配置问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SOCKS_PROXY="socks5://127.0.0.1:1080"
HTTP_PROXY="http://127.0.0.1:1080"
HTTPS_PROXY="http://127.0.0.1:1080"

echo -e "${BLUE}=== macOS 代理健康检查 ===${NC}"
echo "检查时间: $(date)"
echo

# 1. 检查代理服务器连通性
echo -e "${BLUE}1. 检查代理服务器连通性${NC}"
check_proxy_connectivity() {
    local port=$1
    local name=$2
    
    if nc -z 127.0.0.1 $port 2>/dev/null; then
        echo -e "  ✅ $name (端口 $port) 可达"
        return 0
    else
        echo -e "  ❌ $name (端口 $port) 不可达"
        return 1
    fi
}

check_proxy_connectivity 1080 "SOCKS代理"
check_proxy_connectivity 1086 "HTTP代理"
echo

# 2. 检查环境变量
echo -e "${BLUE}2. 检查环境变量${NC}"
echo "当前代理环境变量："
env | grep -i proxy | while read line; do
    echo "  $line"
done

# 检查端口一致性
if [[ "$http_proxy" == *"1086"* ]] && [[ "$all_proxy" == *"1080"* ]]; then
    echo -e "  ⚠️  ${YELLOW}警告: HTTP代理和SOCKS代理使用不同端口${NC}"
    echo "     建议统一使用同一端口以避免混乱"
fi
echo

# 3. 检查系统代理设置
echo -e "${BLUE}3. 检查系统代理设置${NC}"
wifi_proxy=$(networksetup -getsocksfirewallproxy Wi-Fi 2>/dev/null || echo "获取失败")
echo "Wi-Fi SOCKS代理设置："
echo "$wifi_proxy" | sed 's/^/  /'
echo

# 4. 检查常用工具配置
echo -e "${BLUE}4. 检查常用工具代理配置${NC}"

# Git
echo "Git 代理配置："
git_http=$(git config --global --get http.proxy 2>/dev/null || echo "未设置")
git_https=$(git config --global --get https.proxy 2>/dev/null || echo "未设置")
echo "  HTTP: $git_http"
echo "  HTTPS: $git_https"

# npm
echo "npm 代理配置："
npm_proxy=$(npm config get proxy 2>/dev/null || echo "未设置")
npm_https_proxy=$(npm config get https-proxy 2>/dev/null || echo "未设置")
echo "  HTTP: $npm_proxy"
echo "  HTTPS: $npm_https_proxy"

# pip
echo "pip 代理配置："
if [ -f ~/.pip/pip.conf ]; then
    echo "  配置文件存在: ~/.pip/pip.conf"
    grep -E "proxy|index-url" ~/.pip/pip.conf 2>/dev/null | sed 's/^/    /' || echo "    无代理配置"
else
    echo "  配置文件不存在: ~/.pip/pip.conf"
fi
echo

# 5. 网络连接测试
echo -e "${BLUE}5. 网络连接测试${NC}"

test_connection() {
    local proxy_url=$1
    local test_url=$2
    local name=$3
    
    echo -n "  测试 $name: "
    if timeout 10 curl -x "$proxy_url" -s --max-time 5 "$test_url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

test_connection "$SOCKS_PROXY" "http://httpbin.org/ip" "SOCKS代理访问外网"
test_connection "$HTTP_PROXY" "http://httpbin.org/ip" "HTTP代理访问外网"

# 测试直连
echo -n "  测试直连: "
if timeout 10 curl -s --max-time 5 "http://httpbin.org/ip" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 成功${NC}"
else
    echo -e "${RED}❌ 失败${NC}"
fi
echo

# 6. 问题诊断和建议
echo -e "${BLUE}6. 问题诊断和修复建议${NC}"

issues_found=0

# 检查端口一致性
if [[ "$http_proxy" == *"1086"* ]] && [[ "$all_proxy" == *"1080"* ]]; then
    echo -e "  ⚠️  ${YELLOW}问题 1: 代理端口不一致${NC}"
    echo "     修复建议: 统一使用端口 1080"
    echo "     执行: export http_proxy=http://127.0.0.1:1080"
    echo "     执行: export https_proxy=http://127.0.0.1:1080"
    issues_found=$((issues_found + 1))
fi

# 检查 Git 配置
if [[ "$git_http" == "未设置" ]]; then
    echo -e "  ⚠️  ${YELLOW}问题 2: Git 未配置代理${NC}"
    echo "     修复建议: 设置 Git 全局代理"
    echo "     执行: git config --global http.proxy http://127.0.0.1:1080"
    echo "     执行: git config --global https.proxy http://127.0.0.1:1080"
    issues_found=$((issues_found + 1))
fi

# 检查 npm 配置
if [[ "$npm_proxy" == "未设置" ]] || [[ "$npm_proxy" == "null" ]]; then
    echo -e "  ⚠️  ${YELLOW}问题 3: npm 未配置代理${NC}"
    echo "     修复建议: 设置 npm 代理"
    echo "     执行: npm config set proxy http://127.0.0.1:1080"
    echo "     执行: npm config set https-proxy http://127.0.0.1:1080"
    issues_found=$((issues_found + 1))
fi

# 检查 pip 配置
if [ ! -f ~/.pip/pip.conf ]; then
    echo -e "  ⚠️  ${YELLOW}问题 4: pip 未配置代理${NC}"
    echo "     修复建议: 创建 pip 配置文件"
    echo "     执行: mkdir -p ~/.pip"
    echo "     执行: 创建 ~/.pip/pip.conf 配置文件"
    issues_found=$((issues_found + 1))
fi

if [ $issues_found -eq 0 ]; then
    echo -e "  ${GREEN}✅ 未发现明显问题${NC}"
else
    echo -e "  ${RED}发现 $issues_found 个需要修复的问题${NC}"
fi

echo
echo -e "${BLUE}=== 检查完成 ===${NC}"
echo "如需自动修复，请运行: ./proxy-auto-fix.sh"
