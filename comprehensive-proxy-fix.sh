#!/bin/bash

# 综合代理修复脚本 - 解决所有检测到的问题
echo "🔧 开始综合代理修复..."

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# 1. 修复代理函数定义问题
echo -e "${BLUE}1. 修复代理函数定义${NC}"

# 清理.zshrc中的重复配置
echo "清理.zshrc中的重复配置..."
cp ~/.zshrc ~/.zshrc.backup_comprehensive_$(date +%Y%m%d_%H%M%S)

# 移除所有代理相关配置
sed -i '' '/### <<< proxy-block-BEGIN/,/### >>> proxy-block-END/d' ~/.zshrc
sed -i '' '/# 代理设置/,/# 代理设置结束/d' ~/.zshrc

# 添加完整的代理配置块
cat >> ~/.zshrc << 'EOF'

### <<< proxy-block-BEGIN <<<
# macOS 全局代理配置 - 最终版本

# 代理环境变量
export http_proxy="http://127.0.0.1:1086"
export https_proxy="http://127.0.0.1:1086"
export all_proxy="socks5://127.0.0.1:1080"
export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"

# 代理管理函数
proxy_on() {
    export http_proxy="http://127.0.0.1:1086"
    export https_proxy="http://127.0.0.1:1086"
    export all_proxy="socks5://127.0.0.1:1080"
    export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
    
    # 配置开发工具
    git config --global http.proxy "http://127.0.0.1:1086" 2>/dev/null
    git config --global https.proxy "http://127.0.0.1:1086" 2>/dev/null
    npm config set proxy "http://127.0.0.1:1086" >/dev/null 2>&1
    npm config set https-proxy "http://127.0.0.1:1086" >/dev/null 2>&1
    
    echo "✅ 代理已开启 (HTTP:1086, SOCKS:1080)"
}

proxy_off() {
    unset http_proxy https_proxy all_proxy no_proxy
    git config --global --unset http.proxy 2>/dev/null || true
    git config --global --unset https.proxy 2>/dev/null || true
    npm config delete proxy >/dev/null 2>&1 || true
    npm config delete https-proxy >/dev/null 2>&1 || true
    echo "✅ 代理已关闭"
}

proxy_status() {
    echo "=== 代理状态检查 ==="
    if [ -n "$http_proxy" ]; then
        echo "✅ 代理状态：开启"
        echo "   HTTP代理：$http_proxy"
        echo "   HTTPS代理：$https_proxy"
        echo "   SOCKS代理：$all_proxy"
        echo "   绕过规则：$no_proxy"
    else
        echo "❌ 代理状态：关闭"
    fi
    
    # 检查服务状态
    if lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN; then
        echo "✅ 代理服务器正在运行"
    else
        echo "❌ 代理服务器未运行"
    fi
}

proxy_test() {
    echo "🔍 测试代理连接..."
    
    # 检查服务状态
    if ! (lsof -i :1080 | grep -q LISTEN && lsof -i :1086 | grep -q LISTEN); then
        echo "❌ 代理服务器未运行"
        return 1
    fi
    
    # 保存当前no_proxy设置
    local saved_no_proxy="$no_proxy"
    
    # 临时清除no_proxy以确保测试走代理
    unset no_proxy
    
    # 测试SOCKS代理
    echo -n "   SOCKS代理测试: "
    local socks_ip=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    if [ -n "$socks_ip" ] && [ "$socks_ip" != "127.0.0.1" ]; then
        echo "✅ 正常 (IP: $socks_ip)"
    else
        echo "❌ 失败"
    fi
    
    # 测试HTTP代理
    echo -n "   HTTP代理测试: "
    local http_ip=$(timeout 10 curl -x http://127.0.0.1:1086 -s http://httpbin.org/ip 2>/dev/null | grep -o '"origin": "[^"]*"' | cut -d'"' -f4)
    if [ -n "$http_ip" ] && [ "$http_ip" != "127.0.0.1" ]; then
        echo "✅ 正常 (IP: $http_ip)"
    else
        echo "❌ 失败"
    fi
    
    # 测试被墙网站访问
    echo -n "   被墙网站测试: "
    local twitter_code=$(timeout 10 curl -x socks5://127.0.0.1:1080 -s -o /dev/null -w "%{http_code}" https://twitter.com 2>/dev/null)
    if [ "$twitter_code" = "200" ] || [ "$twitter_code" = "301" ] || [ "$twitter_code" = "302" ]; then
        echo "✅ 可访问 (HTTP $twitter_code)"
    else
        echo "⚠️  异常 (HTTP $twitter_code)"
    fi
    
    # 恢复no_proxy设置
    if [ -n "$saved_no_proxy" ]; then
        export no_proxy="$saved_no_proxy"
    fi
}

proxy_fix() {
    echo "🔧 自动修复代理配置..."
    
    # 重新设置环境变量
    proxy_on
    
    # 检查并修复系统代理
    networksetup -setwebproxy Wi-Fi 127.0.0.1 1086 2>/dev/null || true
    networksetup -setwebproxystate Wi-Fi on 2>/dev/null || true
    networksetup -setsecurewebproxy Wi-Fi 127.0.0.1 1086 2>/dev/null || true
    networksetup -setsecurewebproxystate Wi-Fi on 2>/dev/null || true
    networksetup -setsocksfirewallproxy Wi-Fi 127.0.0.1 1080 2>/dev/null || true
    networksetup -setsocksfirewallproxystate Wi-Fi on 2>/dev/null || true
    
    echo "✅ 代理配置修复完成"
}

# 便捷别名
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'
alias pfix='proxy_fix'

### >>> proxy-block-END >>>
EOF

echo -e "${GREEN}✅ 代理函数配置已添加到.zshrc${NC}"

# 2. 立即加载新配置
echo -e "${BLUE}2. 加载新的代理配置${NC}"
source ~/.zshrc

# 3. 验证函数定义
echo -e "${BLUE}3. 验证代理函数${NC}"
if type proxy_on >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_on函数已定义${NC}"
else
    echo -e "${RED}❌ proxy_on函数定义失败${NC}"
fi

if type proxy_status >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_status函数已定义${NC}"
else
    echo -e "${RED}❌ proxy_status函数定义失败${NC}"
fi

if type proxy_test >/dev/null 2>&1; then
    echo -e "${GREEN}✅ proxy_test函数已定义${NC}"
else
    echo -e "${RED}❌ proxy_test函数定义失败${NC}"
fi

# 4. 自动开启代理
echo -e "${BLUE}4. 自动开启代理${NC}"
proxy_on

# 5. 修复系统代理设置
echo -e "${BLUE}5. 确保系统代理设置正确${NC}"
proxy_fix

# 6. 运行测试
echo -e "${BLUE}6. 运行代理连接测试${NC}"
proxy_test

echo
echo -e "${GREEN}🎉 综合修复完成！${NC}"
echo
echo -e "${BLUE}📋 可用命令：${NC}"
echo "   proxy_on / pon       # 开启代理"
echo "   proxy_off / poff     # 关闭代理"
echo "   proxy_status / pstatus # 查看状态"
echo "   proxy_test / ptest   # 测试连接"
echo "   proxy_fix / pfix     # 修复配置"
echo
echo -e "${BLUE}🎯 当前状态：${NC}"
proxy_status
