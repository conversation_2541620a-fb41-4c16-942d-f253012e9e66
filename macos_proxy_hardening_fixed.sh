#!/usr/bin/env bash
# macOS proxy hardening & one-shot fixer for Augment agent (v2)
# -------------------------------------------------------------
# - Consolidates SOCKS :1080  + HTTP :1086
# - Ensures single v2ray instance via launchd (bootstrap)
# - Installs proxy_on / proxy_off / proxy_status for ALL zsh shells (via ~/.proxy_helpers.zsh + ~/.zshenv)
# - Syncs git|npm|pip proxy, sets system-wide proxies, cleans legacy blocks
# Usage: bash macos_proxy_hardening_fixed.sh

set -euo pipefail

### --- configurable constants --- ###
SOCKS_PORT=1080
HTTP_PORT=1086
V2RAY_BIN="/usr/local/bin/v2ray"          # adjust if your v2ray lives elsewhere
V2RAY_CONFIG="$HOME/.config/v2ray/config.json"
PROXY_HELPER="$HOME/.proxy_helpers.zsh"
PROXY_BLOCK_BEGIN="### <<< proxy-block-BEGIN"
PROXY_BLOCK_END="### >>> proxy-block-END"
PLIST_PATH="$HOME/Library/LaunchAgents/com.local.v2ray.plist"

### 0. sanity: v2ray config MUST expose HTTP inbound on ${HTTP_PORT} ###
# Check for both string and numeric port formats
if ! grep -q "\"port\"[[:space:]]*:[[:space:]]*\"${HTTP_PORT}\"" "$V2RAY_CONFIG" && \
   ! grep -q "\"port\"[[:space:]]*:[[:space:]]*${HTTP_PORT}" "$V2RAY_CONFIG"; then
  echo "❌  v2ray config missing HTTP inbound on ${HTTP_PORT}.  Please add before运行" >&2
  
  # Try to find config in alternative locations
  if [ -f "$HOME/.V2rayU/config.json" ]; then
    echo "📋 Found v2ray config at ~/.V2rayU/config.json, copying to standard location..."
    mkdir -p "$(dirname "$V2RAY_CONFIG")"
    cp "$HOME/.V2rayU/config.json" "$V2RAY_CONFIG"
  else
    exit 1
  fi
fi

### 1. kill rogue v2ray processes & load clean launchd service ###
pkill -f "${V2RAY_BIN} run" || true

cat > "${PLIST_PATH}" <<PLIST
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>Label</key><string>com.local.v2ray</string>
  <key>ProgramArguments</key>
  <array>
    <string>${V2RAY_BIN}</string>
    <string>run</string>
    <string>-c</string>
    <string>${V2RAY_CONFIG}</string>
  </array>
  <key>RunAtLoad</key><true/>
  <key>KeepAlive</key><true/>
  <key>StandardOutPath</key><string>/tmp/v2ray.log</string>
  <key>StandardErrorPath</key><string>/tmp/v2ray.err</string>
</dict>
</plist>
PLIST

launchctl bootout gui/$(id -u) "${PLIST_PATH}" 2>/dev/null || true
launchctl bootstrap gui/$(id -u) "${PLIST_PATH}"

### 2. write proxy helper functions (idempotent) ###
# Fixed: Remove quotes from heredoc to allow variable substitution
cat > "$PROXY_HELPER" <<ZSH
export http_proxy="http://127.0.0.1:${HTTP_PORT}"
export https_proxy="http://127.0.0.1:${HTTP_PORT}"
export all_proxy="socks5://127.0.0.1:${SOCKS_PORT}"

proxy_on() {
  export http_proxy="http://127.0.0.1:${HTTP_PORT}"
  export https_proxy="http://127.0.0.1:${HTTP_PORT}"
  export all_proxy="socks5://127.0.0.1:${SOCKS_PORT}"
  export no_proxy="localhost,127.0.0.1,::1,10.0.0.0/8,**********/12,***********/16"
  
  # Configure development tools
  git config --global http.proxy "http://127.0.0.1:${HTTP_PORT}" 2>/dev/null || true
  git config --global https.proxy "http://127.0.0.1:${HTTP_PORT}" 2>/dev/null || true
  npm config set proxy "http://127.0.0.1:${HTTP_PORT}" >/dev/null 2>&1 || true
  npm config set https-proxy "http://127.0.0.1:${HTTP_PORT}" >/dev/null 2>&1 || true
  
  # Configure pip
  mkdir -p \$HOME/.pip
  cat > \$HOME/.pip/pip.conf <<PIP
[global]
proxy = http://127.0.0.1:${HTTP_PORT}
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
PIP
  
  echo "✅ 代理已开启 (HTTP:${HTTP_PORT}, SOCKS:${SOCKS_PORT})"
}

proxy_off() {
  unset http_proxy https_proxy all_proxy no_proxy
  git config --global --unset http.proxy 2>/dev/null || true
  git config --global --unset https.proxy 2>/dev/null || true
  npm config delete proxy >/dev/null 2>&1 || true
  npm config delete https-proxy >/dev/null 2>&1 || true
  rm -f \$HOME/.pip/pip.conf
  echo "✅ 代理已关闭"
}

proxy_status() {
  echo "=== 代理状态检查 ==="
  if [ -n "\${http_proxy-}" ]; then
    echo "✅ 代理状态：开启"
    echo "   HTTP代理：\${http_proxy-}"
    echo "   HTTPS代理：\${https_proxy-}"
    echo "   SOCKS代理：\${all_proxy-}"
    echo "   绕过规则：\${no_proxy-}"
  else
    echo "❌ 代理状态：关闭"
  fi
  
  # Check service status
  if lsof -i :${SOCKS_PORT} | grep -q LISTEN && lsof -i :${HTTP_PORT} | grep -q LISTEN; then
    echo "✅ 代理服务器正在运行"
  else
    echo "❌ 代理服务器未运行"
  fi
}

proxy_test() {
  echo "🔍 测试代理连接..."
  
  # Check service status
  if ! (lsof -i :${SOCKS_PORT} | grep -q LISTEN && lsof -i :${HTTP_PORT} | grep -q LISTEN); then
    echo "❌ 代理服务器未运行"
    return 1
  fi
  
  # Save and clear no_proxy for testing
  local saved_no_proxy="\$no_proxy"
  unset no_proxy
  
  # Test SOCKS proxy with a reliable site
  echo -n "   SOCKS代理测试: "
  if timeout 10 curl -x socks5://127.0.0.1:${SOCKS_PORT} -s -o /dev/null -w "%{http_code}" http://www.baidu.com 2>/dev/null | grep -q "200"; then
    echo "✅ 连接正常"
  else
    echo "❌ 连接失败"
  fi
  
  # Test HTTP proxy
  echo -n "   HTTP代理测试: "
  if timeout 10 curl -x http://127.0.0.1:${HTTP_PORT} -s -o /dev/null -w "%{http_code}" http://www.baidu.com 2>/dev/null | grep -q "200"; then
    echo "✅ 连接正常"
  else
    echo "❌ 连接失败"
  fi
  
  # Restore no_proxy
  if [ -n "\$saved_no_proxy" ]; then
    export no_proxy="\$saved_no_proxy"
  fi
}

# Convenient aliases
alias pon='proxy_on'
alias poff='proxy_off'
alias pstatus='proxy_status'
alias ptest='proxy_test'
ZSH

### 3. ensure helper is sourced by ALL zsh shells ###
# Clean up old proxy configurations first
for f in "$HOME/.zshrc" "$HOME/.zshenv"; do
  if [ -f "$f" ]; then
    # Remove old proxy blocks
    sed -i '' "/${PROXY_BLOCK_BEGIN}/,/${PROXY_BLOCK_END}/d" "$f" 2>/dev/null || true
    sed -i '' '/# 代理设置/,/# 代理设置结束/d' "$f" 2>/dev/null || true
  fi
done

# Add source line to .zshenv for global availability
if ! grep -q "source.*proxy_helpers.zsh" "$HOME/.zshenv" 2>/dev/null; then
  echo "source $PROXY_HELPER" >> "$HOME/.zshenv"
fi

### 4. set system network-wide proxies (Wi-Fi service) ###
networksetup -setwebproxy          "Wi-Fi" 127.0.0.1 "${HTTP_PORT}"
networksetup -setsecurewebproxy    "Wi-Fi" 127.0.0.1 "${HTTP_PORT}"
networksetup -setsocksfirewallproxy "Wi-Fi" 127.0.0.1 "${SOCKS_PORT}"
networksetup -setwebproxystate         "Wi-Fi" on
networksetup -setsecurewebproxystate   "Wi-Fi" on
networksetup -setsocksfirewallproxystate "Wi-Fi" on

### 5. health checks ###
sleep 2  # Give v2ray time to start

if ! lsof -PiTCP:${HTTP_PORT} -sTCP:LISTEN -n | grep -q v2ray; then
  echo "❌  v2ray 未监听 HTTP 端口 ${HTTP_PORT}" >&2
  echo "📋 检查日志: tail -f /tmp/v2ray.log /tmp/v2ray.err"
else
  echo "✅  v2ray HTTP 端口 ${HTTP_PORT} 正在监听"
fi

if ! lsof -PiTCP:${SOCKS_PORT} -sTCP:LISTEN -n | grep -q v2ray; then
  echo "❌  v2ray 未监听 SOCKS 端口 ${SOCKS_PORT}" >&2
else
  echo "✅  v2ray SOCKS 端口 ${SOCKS_PORT} 正在监听"
fi

printf "\n✅ Proxy hardening v2 complete. 重开终端或 'source ~/.zshenv' 生效。\n"
printf "💡 使用方法:\n"
printf "   proxy_on / pon       # 开启代理\n"
printf "   proxy_off / poff     # 关闭代理\n"
printf "   proxy_status / pstatus # 查看状态\n"
printf "   proxy_test / ptest   # 测试连接\n"
